#!/usr/bin/env python3
"""
测试Trainer修复的脚本
验证training_step参数错误是否已解决
"""

import torch
import logging
from transformers import Trainer, TrainingArguments

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_trainer_compatibility():
    """测试Trainer兼容性"""
    logger.info("🧪 测试Trainer兼容性...")
    
    # 创建简单的模型和数据
    class SimpleModel(torch.nn.Module):
        def __init__(self):
            super().__init__()
            self.linear = torch.nn.Linear(10, 1)
        
        def forward(self, input_ids, attention_mask=None, labels=None, **kwargs):
            output = self.linear(input_ids.float())
            loss = None
            if labels is not None:
                loss = torch.nn.functional.mse_loss(output.squeeze(), labels.float())
            
            # 返回类似transformers模型的输出
            class ModelOutput:
                def __init__(self, loss=None, logits=None):
                    self.loss = loss
                    self.logits = logits
            
            return ModelOutput(loss=loss, logits=output)
    
    # 创建简单数据集
    class SimpleDataset:
        def __init__(self, size=10):
            self.size = size
        
        def __len__(self):
            return self.size
        
        def __getitem__(self, idx):
            return {
                'input_ids': torch.randn(10),
                'attention_mask': torch.ones(10),
                'labels': torch.randn(1)
            }
    
    # 简单的数据整理函数
    def simple_collator(batch):
        return {
            'input_ids': torch.stack([item['input_ids'] for item in batch]),
            'attention_mask': torch.stack([item['attention_mask'] for item in batch]),
            'labels': torch.stack([item['labels'] for item in batch]).squeeze()
        }
    
    try:
        # 创建模型和数据集
        model = SimpleModel()
        dataset = SimpleDataset(5)
        
        # 确保梯度计算启用
        torch.set_grad_enabled(True)
        model.train()
        
        # 创建训练参数
        training_args = TrainingArguments(
            output_dir="./test_output",
            per_device_train_batch_size=2,
            num_train_epochs=0.01,
            learning_rate=1e-3,
            logging_steps=1,
            save_steps=1000,
            remove_unused_columns=False,
            dataloader_num_workers=0,
            report_to="none",
        )
        
        # 创建标准Trainer
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=dataset,
            data_collator=simple_collator,
        )
        
        logger.info("✅ Trainer创建成功")
        
        # 测试获取数据加载器
        dataloader = trainer.get_train_dataloader()
        batch = next(iter(dataloader))
        
        logger.info(f"批次键: {list(batch.keys())}")
        logger.info(f"批次大小: {batch['input_ids'].shape}")
        
        # 测试前向传播
        model.train()
        torch.set_grad_enabled(True)
        
        outputs = model(**batch)
        loss = outputs.loss
        
        logger.info(f"损失值: {loss.item()}")
        logger.info(f"损失requires_grad: {loss.requires_grad}")
        
        if loss.requires_grad:
            logger.info("✅ 前向传播成功")
            
            # 测试反向传播
            loss.backward()
            logger.info("✅ 反向传播成功")
            
            # 检查梯度
            has_grad = any(p.grad is not None for p in model.parameters() if p.requires_grad)
            if has_grad:
                logger.info("✅ 梯度计算成功")
                return True
            else:
                logger.error("❌ 没有梯度")
                return False
        else:
            logger.error("❌ 损失不需要梯度")
            return False
            
    except Exception as e:
        logger.error(f"❌ Trainer测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_step_signature():
    """测试training_step方法签名"""
    logger.info("🔍 检查training_step方法签名...")
    
    try:
        import inspect
        
        # 检查Trainer.training_step的签名
        sig = inspect.signature(Trainer.training_step)
        params = list(sig.parameters.keys())
        
        logger.info(f"Trainer.training_step参数: {params}")
        
        # 检查参数数量
        param_count = len(params)
        logger.info(f"参数数量: {param_count}")
        
        if param_count == 3:  # self, model, inputs
            logger.info("✅ 标准3参数签名")
        elif param_count == 4:  # self, model, inputs, num_items_in_batch
            logger.info("✅ 扩展4参数签名")
        else:
            logger.warning(f"⚠️ 非标准参数数量: {param_count}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 签名检查失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始Trainer修复测试...")
    
    # 确保梯度计算启用
    torch.set_grad_enabled(True)
    
    # 运行测试
    results = []
    
    # 1. 检查方法签名
    results.append(test_training_step_signature())
    
    # 2. 测试Trainer兼容性
    results.append(test_trainer_compatibility())
    
    # 总结
    logger.info("📊 测试结果:")
    logger.info(f"方法签名检查: {'✅ 通过' if results[0] else '❌ 失败'}")
    logger.info(f"Trainer兼容性: {'✅ 通过' if results[1] else '❌ 失败'}")
    
    if all(results):
        logger.info("🎉 所有测试通过！")
        logger.info("💡 Trainer修复成功，可以正常使用标准Trainer")
    else:
        logger.error("❌ 部分测试失败")
        logger.info("💡 建议检查transformers库版本或使用自定义Trainer")
    
    logger.info("🎯 测试完成")

if __name__ == "__main__":
    main()
