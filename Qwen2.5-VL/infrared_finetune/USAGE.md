# 红外微调项目使用指南

## 🎯 项目概述

本项目是一个完整的红外视频目标检测微调解决方案，基于Qwen2.5-VL模型，包含以下四个核心模块：

1. **数据预处理** (`data_processor.py`) - 将YOLO格式数据转换为微调格式
2. **微调训练** (`train_fixed_frames.py`) - 使用LoRA技术进行模型微调
3. **高帧频预测** (`inference.py`) - 基于微调模型进行目标检测
4. **评估系统** (`evaluator.py`) - 完整的性能评估

## 🚀 快速开始

### 步骤1: 环境准备

```bash
# 激活Qwen conda环境
conda activate Qwen

# 进入项目目录
cd /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune
```

### 步骤2: 运行完整流程

```bash
# 运行所有步骤
python main.py --step all

# 或者分步运行
python main.py --step preprocess  # 数据预处理
python main.py --step train       # 微调训练
python main.py --step inference   # 目标预测
python main.py --step evaluate    # 模型评估
```

### 步骤3: 查看结果

```bash
# 查看生成的文件
ls -la data/                    # 训练数据
ls -la output/infrared_lora/    # 微调模型
ls -la output/predictions/      # 预测结果
```

## 📁 项目结构

```
infrared_finetune/
├── main.py                     # 🎮 主控制脚本
├── data_processor.py           # 📊 数据预处理模块
├── train_fixed_frames.py       # 🎯 LoRA微调训练模块
├── inference.py               # 🔍 高帧频目标预测模块
├── evaluator.py               # 📈 评估模块
├── test_pipeline.py           # 🧪 测试验证脚本
├── README.md                  # 📖 项目说明
├── USAGE.md                   # 📋 使用指南
├── data/                      # 📂 数据目录
│   ├── videos/               # 🎬 视频文件
│   ├── infrared_video_train.json  # 📝 训练数据
│   └── infrared_video_test.json   # 📝 测试数据
└── output/                    # 📤 输出目录
    ├── infrared_lora/        # 🤖 微调模型
    ├── predictions/          # 🎯 预测结果
    └── evaluation/           # 📊 评估结果
```

## 🔧 详细配置

### 数据配置

在运行前，请确保以下路径正确：

```python
# 在各个模块中修改这些路径
IMAGE_DATA_PATH = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets/images"
LABEL_DATA_PATH = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets/labels"
BASE_MODEL_PATH = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
```

### 支持的数据序列

- **标准分辨率 (256x256)**: data01, data02, data04, data05, data06, data07
- **高分辨率 (640x512)**: data23, data25, data26

### 目标类别

支持检测以下6类目标：
- `drone` - 无人机
- `car` - 汽车
- `ship` - 船只
- `bus` - 公交车
- `pedestrian` - 行人
- `cyclist` - 骑行者

## 📋 模块详解

### 1. 数据预处理 (`data_processor.py`)

**功能**：
- 读取红外图像序列和YOLO标注
- 生成5帧视频序列（帧间隔=5）
- 转换为官方微调格式
- 按序列划分训练集和测试集

**关键参数**：
```python
frame_interval = 5      # 帧间隔
sequence_length = 5     # 序列长度
frame_percentage = 0.2  # 处理帧的百分比
test_sequences = ['data01', 'data23']  # 测试序列
```

**输出**：
- `data/infrared_video_train.json` - 训练数据
- `data/infrared_video_test.json` - 测试数据
- `data/videos/` - 生成的视频文件

### 2. 微调训练 (`train_fixed_frames.py`)

**功能**：
- 使用LoRA技术进行高效微调
- 支持视频序列输入
- 优化的数据加载和批处理

**关键参数**：
```python
# LoRA配置
r = 32                  # LoRA rank
lora_alpha = 32         # LoRA alpha
lora_dropout = 0.1      # Dropout率

# 训练配置
learning_rate = 1e-5    # 学习率
batch_size = 1          # 批次大小
gradient_accumulation = 16  # 梯度累积
num_epochs = 1          # 训练轮数
```

**输出**：
- `output/infrared_lora/` - LoRA权重文件
- `output/infrared_lora/adapter_config.json` - 配置文件

### 3. 目标预测 (`inference.py`)

**功能**：
- 加载微调后的模型
- 高帧频视频序列处理
- 弱小目标检测优化

**关键参数**：
```python
frame_percentage = 0.2  # 处理帧的百分比
iou_threshold = 0.5     # IoU阈值
confidence_threshold = 0.3  # 置信度阈值
```

**输出**：
- `output/predictions/prediction_sequence_*.json` - 单序列结果
- `output/predictions/all_prediction_results.json` - 所有结果

### 4. 评估系统 (`evaluator.py`)

**功能**：
- IoU-based 检测匹配
- 精确度、召回率、F1分数计算
- 序列级和数据集级评估

**评估指标**：
- Precision (精确度)
- Recall (召回率)
- F1 Score (F1分数)
- Average Precision (平均精确度)

## 🛠️ 故障排除

### 常见问题

1. **CUDA内存不足**
   ```bash
   # 解决方案：减小批次大小，增加梯度累积
   per_device_train_batch_size = 1
   gradient_accumulation_steps = 16
   ```

2. **数据加载失败**
   ```bash
   # 检查数据路径
   ls /home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets/images/
   ls /home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets/labels/
   ```

3. **模型加载失败**
   ```bash
   # 检查模型路径
   ls /home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct/
   ```

### 环境检查

运行测试脚本检查环境：
```bash
python test_pipeline.py
```

### 日志查看

查看详细日志：
```bash
tail -f infrared_finetune.log
```

## 📊 性能优化

### 训练优化
- 使用flash_attention_2加速
- 启用梯度检查点节省内存
- 优化数据加载流程

### 推理优化
- 批量处理提高效率
- 模型量化减少内存
- 缓存优化加速推理

## 🎯 使用示例

### 完整流程示例

```bash
# 1. 激活环境
conda activate Qwen

# 2. 进入项目目录
cd /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune

# 3. 验证环境
python test_pipeline.py

# 4. 运行完整流程
python main.py --step all

# 5. 查看结果
ls output/
```

### 自定义配置示例

```bash
# 只处理特定序列
python data_processor.py  # 修改代码中的序列列表

# 使用不同的训练参数
python train_fixed_frames.py  # 修改训练配置

# 预测特定序列
python inference.py  # 修改序列名称
```

## 📈 结果分析

### 训练结果
- 查看训练日志：`output/infrared_lora/training_log.txt`
- 模型权重：`output/infrared_lora/adapter_model.safetensors`

### 预测结果
- 单序列结果：`output/predictions/prediction_sequence_*.json`
- 汇总结果：`output/predictions/all_prediction_results.json`

### 评估结果
- 详细指标：`output/evaluation/evaluation_results.json`
- 性能报告：查看日志输出

## 🔄 扩展功能

1. **支持更多目标类别** - 修改类别映射
2. **多尺度检测** - 调整图像处理参数
3. **时序一致性优化** - 增强序列建模
4. **实时检测支持** - 优化推理速度

## 📞 技术支持

如遇问题，请：
1. 查看日志文件：`infrared_finetune.log`
2. 运行测试脚本：`python test_pipeline.py`
3. 检查验证报告：`validation_report.json`

---

**项目状态**: ✅ 已完成并验证
**最后更新**: 2025-08-03
**版本**: v1.0
