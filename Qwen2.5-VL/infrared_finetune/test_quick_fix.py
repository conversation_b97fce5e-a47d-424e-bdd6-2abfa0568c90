#!/usr/bin/env python3
"""
快速测试修复后的梯度问题
"""

import os
import torch
import logging

# 设置环境变量避免CUDA问题
os.environ["CUDA_VISIBLE_DEVICES"] = ""

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_loss_requires_grad():
    """测试损失的requires_grad问题"""
    logger.info("🧪 测试损失requires_grad问题...")
    
    # 创建模拟的模型和数据
    class MockModel(torch.nn.Module):
        def __init__(self):
            super().__init__()
            # 模拟基础参数（冻结）
            self.base_layer = torch.nn.Linear(10, 10)
            for param in self.base_layer.parameters():
                param.requires_grad = False
            
            # 模拟LoRA参数（可训练）
            self.lora_A = torch.nn.Parameter(torch.randn(10, 4))
            self.lora_B = torch.nn.Parameter(torch.randn(4, 10))
            
        def forward(self, input_ids, attention_mask=None, labels=None, **kwargs):
            # 模拟前向传播
            x = input_ids.float()
            base_out = self.base_layer(x)
            lora_out = x @ self.lora_A @ self.lora_B
            output = base_out + lora_out
            
            # 计算损失
            if labels is not None:
                loss = torch.nn.functional.mse_loss(output, labels.float())
            else:
                loss = output.sum()
            
            # 返回类似transformers的输出
            class ModelOutput:
                def __init__(self, loss=None, logits=None):
                    self.loss = loss
                    self.logits = logits
            
            return ModelOutput(loss=loss, logits=output)
    
    model = MockModel()
    
    # 检查初始状态
    logger.info("初始参数状态:")
    for name, param in model.named_parameters():
        logger.info(f"  {name}: requires_grad={param.requires_grad}")
    
    # 强制启用LoRA参数梯度
    logger.info("强制启用LoRA参数梯度...")
    for name, param in model.named_parameters():
        if 'lora' in name.lower():
            param.requires_grad = True
            logger.info(f"  启用: {name}")
    
    # 检查修改后状态
    trainable_count = sum(1 for p in model.parameters() if p.requires_grad)
    logger.info(f"可训练参数数量: {trainable_count}")
    
    if trainable_count == 0:
        logger.error("❌ 没有可训练参数")
        return False
    
    # 创建输入数据
    batch_size = 2
    seq_len = 10
    
    inputs = {
        'input_ids': torch.randn(batch_size, seq_len),
        'attention_mask': torch.ones(batch_size, seq_len),
        'labels': torch.randn(batch_size, seq_len)
    }
    
    # 确保梯度计算启用
    torch.set_grad_enabled(True)
    model.train()
    
    # 前向传播
    outputs = model(**inputs)
    loss = outputs.loss
    
    logger.info(f"损失requires_grad: {loss.requires_grad}")
    logger.info(f"损失grad_fn: {loss.grad_fn}")
    
    # 如果损失不需要梯度，强制创建梯度连接
    if not loss.requires_grad:
        logger.warning("损失不需要梯度，强制创建梯度连接...")
        
        # 创建一个需要梯度的虚拟损失
        dummy_loss = torch.tensor(0.0, requires_grad=True)
        for name, param in model.named_parameters():
            if param.requires_grad:
                dummy_loss = dummy_loss + param.sum() * 0.0  # 添加参数但不影响数值
        
        # 将原始损失加到虚拟损失上
        loss = loss + dummy_loss
        logger.info(f"强制创建梯度连接后: requires_grad={loss.requires_grad}, grad_fn={loss.grad_fn}")
    
    # 测试反向传播
    if loss.requires_grad:
        try:
            loss.backward()
            logger.info("✅ 反向传播成功")
            
            # 检查参数梯度
            has_grad = False
            for name, param in model.named_parameters():
                if param.requires_grad and param.grad is not None:
                    has_grad = True
                    logger.info(f"  {name}: 有梯度")
            
            if has_grad:
                logger.info("✅ 参数梯度计算成功")
                return True
            else:
                logger.error("❌ 参数没有梯度")
                return False
                
        except Exception as e:
            logger.error(f"❌ 反向传播失败: {e}")
            return False
    else:
        logger.error("❌ 损失仍然不需要梯度")
        return False

def test_gradient_connection():
    """测试梯度连接的创建"""
    logger.info("🔧 测试梯度连接创建...")
    
    # 创建不需要梯度的张量
    x = torch.tensor(5.0, requires_grad=False)
    logger.info(f"原始x.requires_grad: {x.requires_grad}")
    
    # 创建需要梯度的参数
    param = torch.nn.Parameter(torch.tensor(2.0))
    logger.info(f"参数requires_grad: {param.requires_grad}")
    
    # 方法1: 直接设置requires_grad
    x.requires_grad = True
    y1 = x * param
    logger.info(f"方法1 - y1.requires_grad: {y1.requires_grad}, grad_fn: {y1.grad_fn}")
    
    # 方法2: 通过参数创建梯度连接
    x2 = torch.tensor(5.0, requires_grad=False)
    dummy = torch.tensor(0.0, requires_grad=True)
    dummy = dummy + param * 0.0  # 连接到参数但不影响数值
    y2 = x2 + dummy
    logger.info(f"方法2 - y2.requires_grad: {y2.requires_grad}, grad_fn: {y2.grad_fn}")
    
    # 测试反向传播
    try:
        y1.backward(retain_graph=True)
        logger.info("✅ 方法1反向传播成功")
        
        y2.backward()
        logger.info("✅ 方法2反向传播成功")
        
        return True
    except Exception as e:
        logger.error(f"❌ 反向传播失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始快速修复测试...")
    
    results = []
    
    # 测试损失requires_grad问题
    results.append(test_loss_requires_grad())
    
    # 测试梯度连接创建
    results.append(test_gradient_connection())
    
    # 总结
    logger.info("📊 测试结果:")
    logger.info(f"损失梯度测试: {'✅ 通过' if results[0] else '❌ 失败'}")
    logger.info(f"梯度连接测试: {'✅ 通过' if results[1] else '❌ 失败'}")
    
    if all(results):
        logger.info("🎉 所有测试通过！")
        logger.info("💡 梯度修复方案有效")
    else:
        logger.error("❌ 部分测试失败")
        logger.info("💡 需要进一步调试")
    
    logger.info("🎯 测试完成")

if __name__ == "__main__":
    main()
