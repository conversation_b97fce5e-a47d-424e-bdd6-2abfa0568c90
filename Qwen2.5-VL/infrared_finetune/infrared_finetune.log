2025-08-03 11:34:49,480 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data
2025-08-03 11:34:49,480 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data/videos
2025-08-03 11:34:49,480 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output
2025-08-03 11:34:49,480 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/predictions
2025-08-03 11:34:49,488 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/evaluation
2025-08-03 11:34:49,488 - INFO - ============================================================
2025-08-03 11:34:49,488 - INFO - 步骤 1/4: 数据预处理
2025-08-03 11:34:49,488 - INFO - ============================================================
2025-08-03 11:34:49,488 - INFO - 🚀 开始: 数据预处理
2025-08-03 11:34:50,529 - ERROR - ❌ 失败: 数据预处理
2025-08-03 11:34:50,531 - ERROR - 错误: 
CondaError: Run 'conda init' before 'conda activate'


2025-08-03 11:34:50,532 - ERROR - ❌ 步骤 'preprocess' 失败
2025-08-03 11:35:04,290 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data
2025-08-03 11:35:04,290 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data/videos
2025-08-03 11:35:04,290 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output
2025-08-03 11:35:04,290 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/predictions
2025-08-03 11:35:04,290 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/evaluation
2025-08-03 11:35:04,295 - INFO - ============================================================
2025-08-03 11:35:04,295 - INFO - 步骤 1/4: 数据预处理
2025-08-03 11:35:04,295 - INFO - ============================================================
2025-08-03 11:35:04,295 - INFO - 🚀 开始: 数据预处理
2025-08-03 11:35:05,064 - ERROR - ❌ 失败: 数据预处理
2025-08-03 11:35:05,064 - ERROR - 错误: 
CondaError: Run 'conda init' before 'conda activate'


2025-08-03 11:35:05,064 - ERROR - ❌ 步骤 'preprocess' 失败
2025-08-03 11:36:28,990 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data
2025-08-03 11:36:28,992 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data/videos
2025-08-03 11:36:28,992 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output
2025-08-03 11:36:28,992 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/predictions
2025-08-03 11:36:28,992 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/evaluation
2025-08-03 11:36:28,992 - INFO - ============================================================
2025-08-03 11:36:28,992 - INFO - 步骤 1/4: 数据预处理
2025-08-03 11:36:28,992 - INFO - ============================================================
2025-08-03 11:36:28,992 - ERROR - 程序异常: 'InfraredFinetuneManager' object has no attribute 'activate_conda_and_run'
2025-08-03 11:38:00,582 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data
2025-08-03 11:38:00,582 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data/videos
2025-08-03 11:38:00,586 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output
2025-08-03 11:38:00,586 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/predictions
2025-08-03 11:38:00,586 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/evaluation
2025-08-03 11:38:00,586 - INFO - ============================================================
2025-08-03 11:38:00,590 - INFO - 步骤 1/4: 数据预处理
2025-08-03 11:38:00,590 - INFO - ============================================================
2025-08-03 11:38:00,590 - INFO - 🚀 开始: 数据预处理
2025-08-03 11:38:15,487 - INFO - ✅ 完成: 数据预处理
2025-08-03 11:38:15,487 - INFO - 输出: 📊 数据处理完成!
训练样本: 210
测试样本: 90
总视频数: 300
训练序列: {'data07', 'data25', 'data26', 'data02', 'data06', 'data05', 'data04'}
测试序列: {'data01', 'data23'}

2025-08-03 11:38:15,487 - INFO - ✅ 步骤 'preprocess' 完成
2025-08-03 11:43:09,900 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data
2025-08-03 11:43:09,901 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data/videos
2025-08-03 11:43:09,901 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output
2025-08-03 11:43:09,901 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/predictions
2025-08-03 11:43:09,901 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/evaluation
2025-08-03 11:43:09,901 - INFO - ============================================================
2025-08-03 11:43:09,901 - INFO - 步骤 2/4: 微调训练
2025-08-03 11:43:09,901 - INFO - ============================================================
2025-08-03 11:43:09,901 - INFO - 🚀 开始: LoRA微调训练
2025-08-03 11:47:22,335 - ERROR - ❌ 失败: LoRA微调训练
2025-08-03 11:47:22,336 - ERROR - 错误: INFO:__main__:🚀 开始红外视频LoRA微调...
INFO:__main__:📊 加载数据...
INFO:__main__:✅ 数据: 210 个样本
INFO:__main__:🔤 加载processor...
Using a slow image processor as `use_fast` is unset and a slow processor was saved with this model. `use_fast=True` will be the default behavior in v4.52, even if the model was saved with a slow processor. This will result in minor differences in outputs. You'll still be able to use a slow processor with `use_fast=False`.
INFO:__main__:📝 创建数据集...
INFO:__main__:🤖 加载模型...
INFO:accelerate.utils.modeling:Device 0 seems unavailable, Proceeding to check subsequent devices.
INFO:accelerate.utils.modeling:Device 1 seems unavailable, Proceeding to check subsequent devices.
INFO:accelerate.utils.modeling:Device 2 seems unavailable, Proceeding to check subsequent devices.
INFO:accelerate.utils.modeling:Device 3 seems unavailable, Proceeding to check subsequent devices.

Loading checkpoint shards:   0%|          | 0/5 [00:00<?, ?it/s]
Loading checkpoint shards:  20%|██        | 1/5 [00:00<00:02,  1.53it/s]
Loading checkpoint shards:  40%|████      | 2/5 [00:00<00:01,  2.77it/s]
Loading checkpoint shards:  60%|██████    | 3/5 [00:00<00:00,  4.13it/s]
Loading checkpoint shards:  80%|████████  | 4/5 [00:01<00:00,  5.14it/s]
Loading checkpoint shards: 100%|██████████| 5/5 [00:01<00:00,  4.79it/s]
INFO:__main__:⚙️ 配置LoRA...
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/torch/cuda/__init__.py", line 327, in _lazy_init
    queued_call()
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/torch/cuda/__init__.py", line 195, in _check_capability
    capability = get_device_capability(d)
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/torch/cuda/__init__.py", line 451, in get_device_capability
    prop = get_device_properties(device)
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/torch/cuda/__init__.py", line 469, in get_device_properties
    return _get_device_properties(device)  # type: ignore[name-defined]
RuntimeError: device >= 0 && device < num_gpus INTERNAL ASSERT FAILED at "/opt/conda/conda-bld/pytorch_1720538438429/work/aten/src/ATen/cuda/CUDAContext.cpp":49, please report a bug to PyTorch. device=, num_gpus=

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/train_fixed_frames.py", line 313, in <module>
    main()
  File "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/train_fixed_frames.py", line 265, in main
    training_args = TrainingArguments(
  File "<string>", line 132, in __init__
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/transformers/training_args.py", line 1705, in __post_init__
    if torch.cuda.is_available() and not is_torch_bf16_gpu_available():
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 524, in is_torch_bf16_gpu_available
    return torch.cuda.is_available() and torch.cuda.is_bf16_supported()
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/torch/cuda/__init__.py", line 138, in is_bf16_supported
    device = torch.cuda.current_device()
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/torch/cuda/__init__.py", line 878, in current_device
    _lazy_init()
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/torch/cuda/__init__.py", line 333, in _lazy_init
    raise DeferredCudaCallError(msg) from e
torch.cuda.DeferredCudaCallError: CUDA call failed lazily at initialization with error: device >= 0 && device < num_gpus INTERNAL ASSERT FAILED at "/opt/conda/conda-bld/pytorch_1720538438429/work/aten/src/ATen/cuda/CUDAContext.cpp":49, please report a bug to PyTorch. device=, num_gpus=

CUDA call was originally invoked at:

  File "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/train_fixed_frames.py", line 9, in <module>
    import torch
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/torch/__init__.py", line 1694, in <module>
    _C._initExtension(_manager_path())
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/torch/cuda/__init__.py", line 259, in <module>
    _lazy_call(_check_capability)
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/torch/cuda/__init__.py", line 256, in _lazy_call
    _queued_calls.append((callable, traceback.format_stack()))


2025-08-03 11:47:22,339 - ERROR - ❌ 步骤 'train' 失败
2025-08-03 11:52:22,171 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data
2025-08-03 11:52:22,172 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data/videos
2025-08-03 11:52:22,172 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output
2025-08-03 11:52:22,172 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/predictions
2025-08-03 11:52:22,172 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/evaluation
2025-08-03 11:52:22,172 - INFO - ============================================================
2025-08-03 11:52:22,172 - INFO - 步骤 2/4: 微调训练
2025-08-03 11:52:22,173 - INFO - ============================================================
2025-08-03 11:52:22,173 - INFO - 🚀 开始: LoRA微调训练
2025-08-03 11:55:41,037 - ERROR - ❌ 失败: LoRA微调训练
2025-08-03 11:55:41,038 - ERROR - 错误: INFO:__main__:🚀 开始红外视频LoRA微调...
INFO:__main__:📊 加载数据...
INFO:__main__:✅ 数据: 210 个样本
INFO:__main__:🔤 加载processor...
Using a slow image processor as `use_fast` is unset and a slow processor was saved with this model. `use_fast=True` will be the default behavior in v4.52, even if the model was saved with a slow processor. This will result in minor differences in outputs. You'll still be able to use a slow processor with `use_fast=False`.
INFO:__main__:📝 创建数据集...
INFO:__main__:🤖 加载模型...
INFO:accelerate.utils.modeling:We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).

Loading checkpoint shards:   0%|          | 0/5 [00:00<?, ?it/s]
Loading checkpoint shards:  20%|██        | 1/5 [00:04<00:17,  4.44s/it]
Loading checkpoint shards:  40%|████      | 2/5 [00:10<00:16,  5.63s/it]
Loading checkpoint shards:  60%|██████    | 3/5 [00:13<00:08,  4.02s/it]
Loading checkpoint shards:  80%|████████  | 4/5 [00:15<00:03,  3.50s/it]
Loading checkpoint shards: 100%|██████████| 5/5 [00:17<00:00,  2.97s/it]
Loading checkpoint shards: 100%|██████████| 5/5 [00:17<00:00,  3.55s/it]
INFO:__main__:⚙️ 配置LoRA...
No label_names provided for model class `PeftModelForCausalLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.
INFO:__main__:🎯 开始训练...
INFO:__main__:样本数: 210
INFO:__main__:批次大小: 1
INFO:__main__:梯度累积: 16

  0%|          | 0/13 [00:00<?, ?it/s]INFO:__main__:成功加载视频 data26_seq_012.mp4: 5 帧
INFO:__main__:成功加载视频 data05_seq_007.mp4: 5 帧
INFO:__main__:成功加载视频 data26_seq_040.mp4: 5 帧
INFO:__main__:成功加载视频 data06_seq_010.mp4: 5 帧
INFO:__main__:成功加载视频 data02_seq_018.mp4: 5 帧
INFO:__main__:成功加载视频 data26_seq_030.mp4: 5 帧
INFO:__main__:成功加载视频 data26_seq_028.mp4: 5 帧
INFO:__main__:成功加载视频 data26_seq_058.mp4: 5 帧
INFO:__main__:成功加载视频 data26_seq_006.mp4: 5 帧
INFO:__main__:成功加载视频 data26_seq_020.mp4: 5 帧
INFO:__main__:成功加载视频 data26_seq_038.mp4: 5 帧
INFO:__main__:成功加载视频 data26_seq_008.mp4: 5 帧
INFO:__main__:成功加载视频 data25_seq_002.mp4: 5 帧
INFO:__main__:成功加载视频 data02_seq_013.mp4: 5 帧
INFO:__main__:成功加载视频 data05_seq_012.mp4: 5 帧
INFO:__main__:成功加载视频 data04_seq_007.mp4: 5 帧
INFO:__main__:成功加载视频 data06_seq_005.mp4: 5 帧
/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/torch/utils/checkpoint.py:92: UserWarning: None of the inputs have requires_grad=True. Gradients will be None
  warnings.warn(
`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`...
Traceback (most recent call last):
  File "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/train_fixed_frames.py", line 313, in <module>
    main()
  File "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/train_fixed_frames.py", line 302, in main
    trainer.train()
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/transformers/trainer.py", line 2245, in train
    return inner_training_loop(
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/transformers/trainer.py", line 2560, in _inner_training_loop
    tr_loss_step = self.training_step(model, inputs, num_items_in_batch)
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/transformers/trainer.py", line 3782, in training_step
    self.accelerator.backward(loss, **kwargs)
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/accelerate/accelerator.py", line 2473, in backward
    loss.backward(**kwargs)
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/torch/_tensor.py", line 521, in backward
    torch.autograd.backward(
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/torch/autograd/__init__.py", line 289, in backward
    _engine_run_backward(
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/torch/autograd/graph.py", line 768, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
RuntimeError: element 0 of tensors does not require grad and does not have a grad_fn

  0%|          | 0/13 [00:19<?, ?it/s]

2025-08-03 11:55:41,040 - ERROR - ❌ 步骤 'train' 失败
2025-08-03 12:35:20,731 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data
2025-08-03 12:35:20,732 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data/videos
2025-08-03 12:35:20,733 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output
2025-08-03 12:35:20,733 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/predictions
2025-08-03 12:35:20,738 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/evaluation
2025-08-03 12:35:20,738 - INFO - ============================================================
2025-08-03 12:35:20,738 - INFO - 步骤 2/4: 微调训练
2025-08-03 12:35:20,738 - INFO - ============================================================
2025-08-03 12:35:20,738 - INFO - 🚀 开始: LoRA微调训练
2025-08-03 12:35:49,729 - ERROR - ❌ 失败: LoRA微调训练
2025-08-03 12:35:49,729 - ERROR - 错误: INFO:__main__:🚀 开始红外视频LoRA微调...
INFO:__main__:📊 加载数据...
INFO:__main__:✅ 数据: 210 个样本
INFO:__main__:🔤 加载processor...
Using a slow image processor as `use_fast` is unset and a slow processor was saved with this model. `use_fast=True` will be the default behavior in v4.52, even if the model was saved with a slow processor. This will result in minor differences in outputs. You'll still be able to use a slow processor with `use_fast=False`.
Traceback (most recent call last):
  File "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/train_fixed_frames.py", line 331, in <module>
    main()
  File "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/train_fixed_frames.py", line 249, in main
    processor.pad_token = processor.eos_token
AttributeError: 'Qwen2_5_VLProcessor' object has no attribute 'eos_token'

2025-08-03 12:35:49,729 - ERROR - ❌ 步骤 'train' 失败
2025-08-03 12:36:31,096 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data
2025-08-03 12:36:31,096 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data/videos
2025-08-03 12:36:31,096 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output
2025-08-03 12:36:31,096 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/predictions
2025-08-03 12:36:31,096 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/evaluation
2025-08-03 12:36:31,096 - INFO - ============================================================
2025-08-03 12:36:31,096 - INFO - 步骤 2/4: 微调训练
2025-08-03 12:36:31,096 - INFO - ============================================================
2025-08-03 12:36:31,097 - INFO - 🚀 开始: LoRA微调训练
2025-08-03 12:36:59,199 - ERROR - ❌ 失败: LoRA微调训练
2025-08-03 12:36:59,199 - ERROR - 错误: INFO:__main__:🚀 开始红外视频LoRA微调...
INFO:__main__:📊 加载数据...
INFO:__main__:✅ 数据: 210 个样本
INFO:__main__:🔤 加载processor...
Using a slow image processor as `use_fast` is unset and a slow processor was saved with this model. `use_fast=True` will be the default behavior in v4.52, even if the model was saved with a slow processor. This will result in minor differences in outputs. You'll still be able to use a slow processor with `use_fast=False`.
Traceback (most recent call last):
  File "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/train_fixed_frames.py", line 331, in <module>
    main()
  File "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/train_fixed_frames.py", line 249, in main
    processor.pad_token = processor.pad_token
AttributeError: 'Qwen2_5_VLProcessor' object has no attribute 'pad_token'

2025-08-03 12:36:59,202 - ERROR - ❌ 步骤 'train' 失败
2025-08-03 12:44:51,895 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data
2025-08-03 12:44:51,895 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/data/videos
2025-08-03 12:44:51,895 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output
2025-08-03 12:44:51,895 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/predictions
2025-08-03 12:44:51,895 - INFO - 确保目录存在: /home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/output/evaluation
2025-08-03 12:44:51,895 - INFO - ============================================================
2025-08-03 12:44:51,896 - INFO - 步骤 2/4: 微调训练
2025-08-03 12:44:51,896 - INFO - ============================================================
2025-08-03 12:44:51,896 - INFO - 🚀 开始: LoRA微调训练
2025-08-03 12:48:04,721 - ERROR - ❌ 失败: LoRA微调训练
2025-08-03 12:48:04,722 - ERROR - 错误: INFO:__main__:🚀 开始红外视频LoRA微调...
INFO:__main__:📊 加载数据...
INFO:__main__:✅ 数据: 210 个样本
INFO:__main__:🔤 加载processor...
Using a slow image processor as `use_fast` is unset and a slow processor was saved with this model. `use_fast=True` will be the default behavior in v4.52, even if the model was saved with a slow processor. This will result in minor differences in outputs. You'll still be able to use a slow processor with `use_fast=False`.
INFO:__main__:📝 创建数据集...
INFO:__main__:🤖 加载模型...
INFO:accelerate.utils.modeling:We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).

Loading checkpoint shards:   0%|          | 0/5 [00:00<?, ?it/s]
Loading checkpoint shards:  20%|██        | 1/5 [00:04<00:18,  4.57s/it]
Loading checkpoint shards:  40%|████      | 2/5 [00:10<00:16,  5.59s/it]
Loading checkpoint shards:  60%|██████    | 3/5 [00:17<00:12,  6.25s/it]
Loading checkpoint shards:  80%|████████  | 4/5 [00:23<00:05,  5.98s/it]
Loading checkpoint shards: 100%|██████████| 5/5 [00:25<00:00,  4.46s/it]
Loading checkpoint shards: 100%|██████████| 5/5 [00:25<00:00,  5.06s/it]
INFO:__main__:⚙️ 配置LoRA...
No label_names provided for model class `PeftModelForCausalLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.
INFO:__main__:🎯 开始训练...
INFO:__main__:样本数: 210
INFO:__main__:批次大小: 1
INFO:__main__:梯度累积: 16

  0%|          | 0/13 [00:00<?, ?it/s]INFO:__main__:成功加载视频 data05_seq_007.mp4: 5 帧
INFO:__main__:成功加载视频 data26_seq_012.mp4: 5 帧
INFO:__main__:成功加载视频 data06_seq_010.mp4: 5 帧
INFO:__main__:成功加载视频 data26_seq_040.mp4: 5 帧
INFO:__main__:成功加载视频 data02_seq_018.mp4: 5 帧
INFO:__main__:成功加载视频 data26_seq_030.mp4: 5 帧
INFO:__main__:成功加载视频 data26_seq_020.mp4: 5 帧
INFO:__main__:成功加载视频 data26_seq_006.mp4: 5 帧
INFO:__main__:成功加载视频 data26_seq_058.mp4: 5 帧
INFO:__main__:成功加载视频 data26_seq_028.mp4: 5 帧
INFO:__main__:成功加载视频 data26_seq_038.mp4: 5 帧
INFO:__main__:成功加载视频 data02_seq_013.mp4: 5 帧
INFO:__main__:成功加载视频 data25_seq_002.mp4: 5 帧
INFO:__main__:成功加载视频 data05_seq_012.mp4: 5 帧
INFO:__main__:成功加载视频 data26_seq_008.mp4: 5 帧
INFO:__main__:成功加载视频 data06_seq_005.mp4: 5 帧
INFO:__main__:成功加载视频 data25_seq_015.mp4: 5 帧
INFO:__main__:成功加载视频 data04_seq_007.mp4: 5 帧
INFO:__main__:成功加载视频 data25_seq_041.mp4: 5 帧
INFO:__main__:成功加载视频 data02_seq_008.mp4: 5 帧
INFO:__main__:成功加载视频 data25_seq_019.mp4: 5 帧
INFO:__main__:成功加载视频 data02_seq_023.mp4: 5 帧
INFO:__main__:成功加载视频 data26_seq_032.mp4: 5 帧
INFO:__main__:成功加载视频 data26_seq_011.mp4: 5 帧
INFO:__main__:成功加载视频 data02_seq_022.mp4: 5 帧
/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/torch/utils/checkpoint.py:92: UserWarning: None of the inputs have requires_grad=True. Gradients will be None
  warnings.warn(
`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`...
Traceback (most recent call last):
  File "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/train_fixed_frames.py", line 337, in <module>
    main()
  File "/home/<USER>/Qwen/Qwen2.5-VL/infrared_finetune/train_fixed_frames.py", line 326, in main
    trainer.train()
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/transformers/trainer.py", line 2245, in train
    return inner_training_loop(
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/transformers/trainer.py", line 2560, in _inner_training_loop
    tr_loss_step = self.training_step(model, inputs, num_items_in_batch)
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/transformers/trainer.py", line 3782, in training_step
    self.accelerator.backward(loss, **kwargs)
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/accelerate/accelerator.py", line 2473, in backward
    loss.backward(**kwargs)
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/torch/_tensor.py", line 521, in backward
    torch.autograd.backward(
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/torch/autograd/__init__.py", line 289, in backward
    _engine_run_backward(
  File "/home/<USER>/miniconda3/envs/Qwen/lib/python3.10/site-packages/torch/autograd/graph.py", line 768, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
RuntimeError: element 0 of tensors does not require grad and does not have a grad_fn

  0%|          | 0/13 [00:07<?, ?it/s]

2025-08-03 12:48:04,723 - ERROR - ❌ 步骤 'train' 失败
