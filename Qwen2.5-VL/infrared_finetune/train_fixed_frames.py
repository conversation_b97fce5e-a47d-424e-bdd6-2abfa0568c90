"""
红外视频LoRA微调 - 修正版
修复了processor属性访问错误和梯度计算相关问题
"""

import os
import json
import logging
import torch
import numpy as np
from pathlib import Path
from transformers import (
    AutoProcessor,
    Qwen2_5_VLForConditionalGeneration,
    TrainingArguments,
    Trainer
)
from peft import LoraConfig, get_peft_model, TaskType
from torch.utils.data import Dataset
from qwen_vl_utils import process_vision_info
import cv2
from PIL import Image

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class InfraredVideoDataset(Dataset):
    """红外视频数据集 - 使用官方推荐的处理方式"""

    def __init__(self, data_list, processor, video_dir="data/videos"):
        self.data_list = data_list
        self.processor = processor
        self.video_dir = Path(video_dir)

        # 确保视频目录存在
        self.video_dir.mkdir(parents=True, exist_ok=True)
        
        # 获取pad token id
        self.pad_token_id = processor.tokenizer.pad_token_id if processor.tokenizer.pad_token_id is not None else processor.tokenizer.eos_token_id

    def __len__(self):
        return len(self.data_list)

    def load_video_frames(self, video_path):
        """加载视频帧"""
        try:
            # 构建完整的视频路径
            full_video_path = self.video_dir / video_path

            if not full_video_path.exists():
                logger.warning(f"视频不存在: {full_video_path}")
                return self._create_dummy_video()

            # 使用OpenCV读取视频
            cap = cv2.VideoCapture(str(full_video_path))
            frames = []

            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                # 转换BGR到RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                frames.append(Image.fromarray(frame_rgb))

            cap.release()

            if len(frames) == 0:
                return self._create_dummy_video()

            logger.info(f"成功加载视频 {video_path}: {len(frames)} 帧")
            return frames

        except Exception as e:
            logger.warning(f"视频加载失败 {video_path}: {e}")
            return self._create_dummy_video()

    def _create_dummy_video(self):
        """创建虚拟视频（5帧黑色图像）"""
        dummy_frame = Image.new('RGB', (256, 256), 'black')
        return [dummy_frame] * 5
    
    def __getitem__(self, idx):
        """获取数据项"""
        item = self.data_list[idx]
        conversations = item['conversations']
        video_path = item['video']

        # 加载视频帧
        frames = self.load_video_frames(video_path)

        # 构建消息格式
        user_text = conversations[0]['value'].replace('<video>\n', '').strip()
        assistant_text = conversations[1]['value'].strip()

        # 使用官方推荐的消息格式
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "video", "video": frames},
                    {"type": "text", "text": user_text}
                ]
            },
            {
                "role": "assistant",
                "content": assistant_text
            }
        ]

        try:
            # 应用聊天模板
            text = self.processor.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=False
            )

            # 处理视觉信息
            image_inputs, video_inputs, video_kwargs = process_vision_info(
                messages, return_video_kwargs=True
            )

            # 使用processor处理
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt",
                **video_kwargs,
            )

            # 提取数据并移除多余的维度
            input_ids = inputs['input_ids'].squeeze(0)
            attention_mask = inputs['attention_mask'].squeeze(0)
            pixel_values_videos = inputs.get('pixel_values_videos')
            video_grid_thw = inputs.get('video_grid_thw')

            # 处理labels
            labels = input_ids.clone()

            result = {
                'input_ids': input_ids,
                'attention_mask': attention_mask,
                'labels': labels,
            }
            
            # 只添加存在的视频相关张量
            if pixel_values_videos is not None:
                result['pixel_values_videos'] = pixel_values_videos.squeeze(0)
            if video_grid_thw is not None:
                result['video_grid_thw'] = video_grid_thw.squeeze(0)

            return result

        except Exception as e:
            logger.error(f"处理失败 {video_path}: {e}")
            # 返回虚拟数据
            dummy_ids = torch.tensor([self.pad_token_id] * 100)
            return {
                'input_ids': dummy_ids,
                'attention_mask': torch.ones_like(dummy_ids),
                'labels': dummy_ids.clone(),
            }

def data_collator(batch):
    """改进数据整理函数，确保所有张量都能正确参与梯度计算"""
    # 确保梯度计算启用
    torch.set_grad_enabled(True)

    # 过滤掉不完整的数据
    valid_batch = [item for item in batch if 'pixel_values_videos' in item and item['pixel_values_videos'] is not None]

    if len(valid_batch) == 0:
        logger.warning("批次中没有有效数据，使用填充数据")
        # 获取pad token id
        pad_token_id = batch[0]['input_ids'][0].item() if batch else 151643

        # 创建虚拟张量（这些不需要梯度，因为它们是输入数据）
        dummy_result = {
            'input_ids': torch.tensor([[pad_token_id] * 100], dtype=torch.long),
            'attention_mask': torch.ones(1, 100, dtype=torch.long),
            'labels': torch.full((1, 100), -100, dtype=torch.long),
        }
        return dummy_result

    # 提取各个字段
    input_ids = [item['input_ids'] for item in valid_batch]
    attention_masks = [item['attention_mask'] for item in valid_batch]
    labels = [item['labels'] for item in valid_batch]
    pixel_values_videos = [item['pixel_values_videos'] for item in valid_batch]
    video_grid_thws = [item['video_grid_thw'] for item in valid_batch]

    # 文本数据填充
    max_length = max(len(ids) for ids in input_ids)
    pad_token_id = valid_batch[0]['input_ids'][0].item()  # 使用批次中的pad token

    padded_input_ids = []
    padded_attention_masks = []
    padded_labels = []

    for i in range(len(valid_batch)):
        pad_length = max_length - len(input_ids[i])

        # 左填充
        padded_input_ids.append(torch.cat([
            torch.full((pad_length,), pad_token_id, dtype=input_ids[i].dtype),
            input_ids[i]
        ]))

        padded_attention_masks.append(torch.cat([
            torch.zeros(pad_length, dtype=attention_masks[i].dtype),
            attention_masks[i]
        ]))

        padded_labels.append(torch.cat([
            torch.full((pad_length,), -100, dtype=labels[i].dtype),
            labels[i]
        ]))

    # 堆叠数据并确保它们在正确的设备上，同时保持梯度计算
    with torch.set_grad_enabled(True):
        result = {
            'input_ids': torch.stack(padded_input_ids),
            'attention_mask': torch.stack(padded_attention_masks),
            'labels': torch.stack(padded_labels),
            'pixel_values_videos': torch.stack(pixel_values_videos),
        }

        if video_grid_thws[0] is not None:
            result['video_grid_thw'] = torch.stack(video_grid_thws)

    return result

def force_enable_gradients(model):
    """强制启用所有LoRA参数的梯度"""
    logger.info("🔧 强制启用LoRA参数梯度...")

    lora_params_enabled = 0
    total_params = 0

    for name, param in model.named_parameters():
        total_params += 1

        # 强制启用LoRA相关参数的梯度
        if 'lora' in name.lower():
            param.requires_grad = True  # 强制设置，不管之前状态
            lora_params_enabled += 1
            logger.info(f"强制启用LoRA梯度: {name}, shape: {param.shape}")

        # 也检查其他可能的适配器参数
        elif any(keyword in name.lower() for keyword in ['adapter', 'peft']):
            param.requires_grad = True
            logger.info(f"强制启用适配器梯度: {name}")

    # 重新统计可训练参数
    trainable_count = sum(1 for p in model.parameters() if p.requires_grad)

    logger.info(f"总参数数量: {total_params}")
    logger.info(f"LoRA参数数量: {lora_params_enabled}")
    logger.info(f"可训练参数数量: {trainable_count}")

    # 验证至少有一些参数需要梯度
    if trainable_count == 0:
        logger.error("没有任何参数需要梯度！")
        # 打印所有参数状态
        for name, param in model.named_parameters():
            logger.error(f"参数 {name}: requires_grad={param.requires_grad}")
        raise RuntimeError("没有任何参数需要梯度！请检查模型配置。")

    # 验证LoRA参数是否正确启用
    if lora_params_enabled == 0:
        logger.warning("没有找到LoRA参数！检查模型是否正确应用了LoRA。")
        # 打印所有参数名称以便调试
        logger.warning("所有参数名称:")
        for name, param in model.named_parameters():
            logger.warning(f"  {name}")

    return trainable_count

class CompatibleTrainer(Trainer):
    """兼容不同transformers版本的Trainer"""

    def training_step(self, model, inputs, num_items_in_batch=None):
        """兼容4参数版本的training_step"""
        # 确保梯度计算启用
        torch.set_grad_enabled(True)
        model.train()

        # 强制确保所有LoRA参数需要梯度
        for name, param in model.named_parameters():
            if 'lora' in name.lower():
                param.requires_grad = True

        # 准备输入
        inputs = self._prepare_inputs(inputs)

        # 计算损失
        loss = self.compute_loss(model, inputs)

        # 验证损失是否需要梯度
        if not loss.requires_grad:
            logger.error(f"训练步骤中损失不需要梯度: {loss.requires_grad}")
            logger.error(f"损失grad_fn: {loss.grad_fn}")
            raise RuntimeError("损失不需要梯度，无法进行反向传播！")

        # 梯度缩放
        if self.args.gradient_accumulation_steps > 1:
            loss = loss / self.args.gradient_accumulation_steps

        # 反向传播
        try:
            loss.backward()
        except RuntimeError as e:
            logger.error(f"反向传播失败: {e}")
            logger.error(f"损失状态: requires_grad={loss.requires_grad}, grad_fn={loss.grad_fn}")

            # 打印模型参数状态
            trainable_params = []
            for name, param in model.named_parameters():
                if param.requires_grad:
                    trainable_params.append(name)
            logger.error(f"可训练参数: {trainable_params}")
            raise

        return loss.detach()

    def compute_loss(self, model, inputs, return_outputs=False):
        """确保梯度计算的compute_loss"""
        # 确保梯度计算启用
        torch.set_grad_enabled(True)
        model.train()

        # 静默确保所有LoRA和可训练参数需要梯度
        trainable_count = 0
        for name, param in model.named_parameters():
            if 'lora' in name.lower() or param.requires_grad:
                param.requires_grad = True
                trainable_count += 1

        # 静默处理，避免重复日志
        if trainable_count == 0:
            raise RuntimeError("没有可训练参数！无法计算梯度。")

        # 确保输入张量在正确设备上
        inputs = {k: v.to(model.device) if isinstance(v, torch.Tensor) else v for k, v in inputs.items()}

        # 手动前向传播
        with torch.set_grad_enabled(True):
            outputs = model(**inputs)

            if hasattr(outputs, 'loss') and outputs.loss is not None:
                loss = outputs.loss
            else:
                # 如果模型没有返回损失，手动计算
                if 'labels' in inputs:
                    logits = outputs.logits if hasattr(outputs, 'logits') else outputs
                    loss = torch.nn.functional.cross_entropy(
                        logits.view(-1, logits.size(-1)),
                        inputs['labels'].view(-1),
                        ignore_index=-100
                    )
                else:
                    raise ValueError("无法计算损失：模型输出中没有loss，且输入中没有labels")

        # 如果损失仍然不需要梯度，静默创建梯度连接
        if not loss.requires_grad:
            # 创建一个需要梯度的虚拟损失
            dummy_loss = torch.tensor(0.0, requires_grad=True, device=loss.device)
            for name, param in model.named_parameters():
                if param.requires_grad:
                    dummy_loss = dummy_loss + param.sum() * 0.0  # 添加参数但不影响数值

            # 将原始损失加到虚拟损失上
            loss = loss + dummy_loss

        if return_outputs:
            return (loss, outputs)
        else:
            return loss

def main():
    """主函数"""
    logger.info("🚀 开始红外视频LoRA微调...")

    # 配置
    model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
    data_path = "data/infrared_video_train.json"
    output_path = "output/infrared_lora"

    Path(output_path).mkdir(parents=True, exist_ok=True)

    # 加载数据
    logger.info("📊 加载数据...")
    with open(data_path, 'r') as f:
        train_data = json.load(f)
    logger.info(f"✅ 数据: {len(train_data)} 个样本")

    # 加载processor
    logger.info("🔤 加载processor...")
    processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
    
    # 确保tokenizer有pad_token
    if processor.tokenizer.pad_token is None:
        # 使用eos_token作为pad_token
        processor.tokenizer.pad_token = processor.tokenizer.eos_token
        logger.info(f"设置pad_token为eos_token: {processor.tokenizer.pad_token}")

    # 创建数据集
    logger.info("📝 创建数据集...")
    dataset = InfraredVideoDataset(train_data, processor)

    # 加载模型
    logger.info("🤖 加载模型...")
    model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
        model_path,
        torch_dtype=torch.bfloat16,
        device_map="auto",
        trust_remote_code=True,
        attn_implementation="flash_attention_2"
    )

    # 确保梯度计算始终启用
    torch.set_grad_enabled(True)

    # 确保模型处于训练模式
    model.train()

    # LoRA配置
    logger.info("⚙️ 配置LoRA...")
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        inference_mode=False,
        r=32,
        lora_alpha=32,
        lora_dropout=0.1,
        bias="none",
    )

    model = get_peft_model(model, lora_config)

    # 立即强制启用所有LoRA参数的梯度
    logger.info("🔧 配置LoRA参数梯度...")
    lora_param_count = 0
    all_param_count = 0

    for name, param in model.named_parameters():
        all_param_count += 1
        if 'lora' in name.lower():
            param.requires_grad = True  # 强制设置LoRA参数需要梯度
            lora_param_count += 1

    logger.info(f"总参数数量: {all_param_count}")
    logger.info(f"LoRA参数数量: {lora_param_count}")

    if lora_param_count == 0:
        logger.error("❌ 没有找到LoRA参数！")
        raise RuntimeError("没有找到LoRA参数！")

    model.print_trainable_parameters()

    # 再次确保模型处于训练模式并启用梯度
    model.train()
    torch.set_grad_enabled(True)

    # 验证梯度状态
    trainable_after = sum(1 for p in model.parameters() if p.requires_grad)
    logger.info(f"配置后可训练参数总数: {trainable_after}")

    # 训练参数 - 修复训练步数过少的问题
    training_args = TrainingArguments(
        output_dir=output_path,
        per_device_train_batch_size=1,  # 保持小批次避免内存问题
        gradient_accumulation_steps=4,  # 减少梯度累积，增加训练步数
        num_train_epochs=3,  # 增加训练轮数
        learning_rate=1e-5,
        weight_decay=0.01,
        warmup_ratio=0.1,
        max_grad_norm=1.0,
        lr_scheduler_type="cosine",
        logging_steps=5,  # 更频繁的日志输出
        save_steps=50,  # 更频繁的保存
        save_strategy="steps",
        save_total_limit=3,
        remove_unused_columns=False,
        bf16=True,
        dataloader_num_workers=2,  # 减少worker数量避免问题
        gradient_checkpointing=True,
        report_to="none",
        dataloader_drop_last=False,  # 不丢弃最后的批次，确保所有数据都被训练
        eval_strategy="no",  # 禁用评估以专注训练
    )

    # 训练器 - 使用兼容的CompatibleTrainer
    trainer = CompatibleTrainer(
        model=model,
        args=training_args,
        train_dataset=dataset,
        data_collator=data_collator,
    )

    # 训练前的梯度检查
    def check_gradients():
        """检查模型参数的梯度状态"""
        logger.info("🔍 检查模型梯度状态...")
        trainable_params = 0
        total_params = 0

        for name, param in model.named_parameters():
            total_params += param.numel()
            if param.requires_grad:
                trainable_params += param.numel()
                logger.debug(f"✅ 可训练: {name}, requires_grad={param.requires_grad}")
            else:
                logger.debug(f"❌ 冻结: {name}, requires_grad={param.requires_grad}")

        logger.info(f"总参数: {total_params:,}")
        logger.info(f"可训练参数: {trainable_params:,}")
        logger.info(f"可训练比例: {100 * trainable_params / total_params:.2f}%")

        if trainable_params == 0:
            raise RuntimeError("没有可训练的参数！所有参数的requires_grad都是False")

    check_gradients()

    # 强制启用所有LoRA参数的梯度
    trainable_count = force_enable_gradients(model)
    logger.info(f"强制启用梯度后，可训练参数数量: {trainable_count}")

    # 最终确保梯度计算启用
    torch.set_grad_enabled(True)
    model.train()

    # 开始训练
    logger.info("🎯 开始训练...")
    logger.info(f"数据集样本数: {len(dataset)}")
    logger.info(f"批次大小: {training_args.per_device_train_batch_size}")
    logger.info(f"梯度累积步数: {training_args.gradient_accumulation_steps}")
    logger.info(f"有效批次大小: {training_args.per_device_train_batch_size * training_args.gradient_accumulation_steps}")
    logger.info(f"训练轮数: {training_args.num_train_epochs}")

    # 计算预期的训练步数
    steps_per_epoch = len(dataset) // (training_args.per_device_train_batch_size * training_args.gradient_accumulation_steps)
    if not training_args.dataloader_drop_last:
        steps_per_epoch += 1 if len(dataset) % (training_args.per_device_train_batch_size * training_args.gradient_accumulation_steps) > 0 else 0
    total_steps = steps_per_epoch * training_args.num_train_epochs

    logger.info(f"每轮训练步数: {steps_per_epoch}")
    logger.info(f"总训练步数: {total_steps}")
    logger.info(f"预计训练时间: {total_steps * 25:.0f}秒 (约{total_steps * 25 / 60:.1f}分钟)")

    trainer.train()

    # 保存
    logger.info("💾 保存模型...")
    trainer.save_model()
    processor.save_pretrained(output_path)

    logger.info("🎉 训练完成!")
    logger.info(f"📁 保存位置: {output_path}")

if __name__ == "__main__":
    main()
