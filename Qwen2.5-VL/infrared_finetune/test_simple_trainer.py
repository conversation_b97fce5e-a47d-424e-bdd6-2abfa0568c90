#!/usr/bin/env python3
"""
简单的Trainer测试，避免CUDA问题
"""

import os
import torch
import logging

# 设置环境变量避免CUDA问题
os.environ["CUDA_VISIBLE_DEVICES"] = ""

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_trainer_import():
    """测试Trainer导入和基本功能"""
    logger.info("🧪 测试Trainer导入...")
    
    try:
        from train_fixed_frames import CompatibleTrainer
        logger.info("✅ CompatibleTrainer导入成功")
        
        # 检查方法是否存在
        if hasattr(CompatibleTrainer, 'training_step'):
            logger.info("✅ training_step方法存在")
        else:
            logger.error("❌ training_step方法不存在")
            return False
        
        if hasattr(CompatibleTrainer, 'compute_loss'):
            logger.info("✅ compute_loss方法存在")
        else:
            logger.error("❌ compute_loss方法不存在")
            return False
        
        # 检查方法签名
        import inspect
        sig = inspect.signature(CompatibleTrainer.training_step)
        params = list(sig.parameters.keys())
        logger.info(f"training_step参数: {params}")
        
        # 应该有3或4个参数（包括self）
        if len(params) >= 3:
            logger.info("✅ 参数数量正确")
            return True
        else:
            logger.error(f"❌ 参数数量不正确: {len(params)}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gradient_functions():
    """测试梯度相关函数"""
    logger.info("🔧 测试梯度函数...")
    
    try:
        # 确保梯度计算启用
        torch.set_grad_enabled(True)
        logger.info(f"torch.is_grad_enabled(): {torch.is_grad_enabled()}")
        
        # 测试简单的梯度计算
        x = torch.tensor([1.0, 2.0], requires_grad=True)
        y = x.sum()
        y.backward()
        
        if x.grad is not None:
            logger.info("✅ 基础梯度计算正常")
            return True
        else:
            logger.error("❌ 基础梯度计算失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 梯度测试失败: {e}")
        return False

def test_data_collator():
    """测试数据整理函数"""
    logger.info("📊 测试数据整理函数...")
    
    try:
        from train_fixed_frames import data_collator
        logger.info("✅ data_collator导入成功")
        
        # 创建虚拟数据
        dummy_batch = [
            {
                'input_ids': torch.tensor([1, 2, 3]),
                'attention_mask': torch.tensor([1, 1, 1]),
                'labels': torch.tensor([1, 2, 3]),
                'pixel_values_videos': torch.randn(5, 3, 224, 224),
                'video_grid_thw': torch.tensor([[5, 14, 14]])
            }
        ]
        
        # 测试数据整理
        result = data_collator(dummy_batch)
        
        if result is not None and 'input_ids' in result:
            logger.info("✅ 数据整理成功")
            logger.info(f"结果键: {list(result.keys())}")
            return True
        else:
            logger.error("❌ 数据整理失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 数据整理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    logger.info("🚀 开始简单Trainer测试...")
    
    results = []
    
    # 1. 测试导入
    results.append(test_trainer_import())
    
    # 2. 测试梯度函数
    results.append(test_gradient_functions())
    
    # 3. 测试数据整理
    results.append(test_data_collator())
    
    # 总结
    logger.info("📊 测试结果:")
    logger.info(f"Trainer导入: {'✅ 通过' if results[0] else '❌ 失败'}")
    logger.info(f"梯度函数: {'✅ 通过' if results[1] else '❌ 失败'}")
    logger.info(f"数据整理: {'✅ 通过' if results[2] else '❌ 失败'}")
    
    if all(results):
        logger.info("🎉 所有测试通过！")
        logger.info("💡 training_step参数错误已修复")
        logger.info("💡 可以安全运行训练代码")
    else:
        logger.error("❌ 部分测试失败")
        logger.info("💡 请检查代码修改是否正确")
    
    logger.info("🎯 测试完成")

if __name__ == "__main__":
    main()
