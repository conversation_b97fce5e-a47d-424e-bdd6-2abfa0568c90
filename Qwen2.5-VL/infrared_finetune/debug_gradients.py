#!/usr/bin/env python3
"""
梯度调试脚本
用于诊断和修复梯度计算问题
"""

import torch
import logging
from pathlib import Path
from transformers import AutoProcessor, Qwen2_5_VLForConditionalGeneration
from peft import LoraConfig, get_peft_model, TaskType

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_torch_grad_state():
    """检查PyTorch梯度计算状态"""
    logger.info("🔍 检查PyTorch梯度计算状态...")
    logger.info(f"torch.is_grad_enabled(): {torch.is_grad_enabled()}")
    logger.info(f"torch.backends.cudnn.enabled: {torch.backends.cudnn.enabled}")
    
    # 确保梯度计算启用
    torch.set_grad_enabled(True)
    logger.info(f"设置后 torch.is_grad_enabled(): {torch.is_grad_enabled()}")

def test_simple_gradient():
    """测试简单的梯度计算"""
    logger.info("🧪 测试简单梯度计算...")
    
    # 创建一个简单的张量
    x = torch.tensor([1.0, 2.0, 3.0], requires_grad=True)
    y = x.sum()
    
    logger.info(f"x.requires_grad: {x.requires_grad}")
    logger.info(f"y.requires_grad: {y.requires_grad}")
    
    # 计算梯度
    y.backward()
    
    logger.info(f"x.grad: {x.grad}")
    
    if x.grad is not None:
        logger.info("✅ 简单梯度计算正常")
    else:
        logger.error("❌ 简单梯度计算失败")

def test_model_gradients():
    """测试模型梯度"""
    logger.info("🤖 测试模型梯度...")
    
    model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
    
    try:
        # 加载模型
        logger.info("加载基础模型...")
        model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.bfloat16,
            device_map="auto",
            trust_remote_code=True
        )
        
        # 确保梯度计算启用
        torch.set_grad_enabled(True)
        model.train()
        
        # 检查基础模型参数
        logger.info("检查基础模型参数...")
        total_params = 0
        trainable_params = 0
        
        for name, param in model.named_parameters():
            total_params += param.numel()
            if param.requires_grad:
                trainable_params += param.numel()
        
        logger.info(f"基础模型 - 总参数: {total_params:,}, 可训练: {trainable_params:,}")
        
        # 应用LoRA
        logger.info("应用LoRA配置...")
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
            inference_mode=False,
            r=32,
            lora_alpha=32,
            lora_dropout=0.1,
            bias="none",
        )
        
        model = get_peft_model(model, lora_config)
        
        # 检查LoRA模型参数
        logger.info("检查LoRA模型参数...")
        total_params = 0
        trainable_params = 0
        lora_params = 0
        
        for name, param in model.named_parameters():
            total_params += param.numel()
            if param.requires_grad:
                trainable_params += param.numel()
                if 'lora' in name.lower():
                    lora_params += param.numel()
                    logger.info(f"LoRA参数: {name}, shape: {param.shape}, requires_grad: {param.requires_grad}")
        
        logger.info(f"LoRA模型 - 总参数: {total_params:,}")
        logger.info(f"LoRA模型 - 可训练参数: {trainable_params:,}")
        logger.info(f"LoRA模型 - LoRA参数: {lora_params:,}")
        
        if trainable_params == 0:
            logger.error("❌ 没有可训练参数！")
            return False
        else:
            logger.info("✅ 模型有可训练参数")
            return True
            
    except Exception as e:
        logger.error(f"❌ 模型测试失败: {e}")
        return False

def test_data_processing():
    """测试数据处理过程中的梯度"""
    logger.info("📊 测试数据处理梯度...")
    
    # 确保梯度计算启用
    torch.set_grad_enabled(True)
    
    # 创建模拟数据
    batch_size = 2
    seq_length = 100
    
    # 模拟输入数据
    input_ids = torch.randint(0, 1000, (batch_size, seq_length))
    attention_mask = torch.ones(batch_size, seq_length)
    labels = input_ids.clone()
    
    logger.info(f"input_ids.requires_grad: {input_ids.requires_grad}")
    logger.info(f"attention_mask.requires_grad: {attention_mask.requires_grad}")
    logger.info(f"labels.requires_grad: {labels.requires_grad}")
    
    # 测试堆叠操作
    stacked_input_ids = torch.stack([input_ids[0], input_ids[1]])
    logger.info(f"stacked_input_ids.requires_grad: {stacked_input_ids.requires_grad}")
    
    logger.info("✅ 数据处理梯度测试完成")

def diagnose_gradient_issue():
    """诊断梯度问题"""
    logger.info("🔧 开始梯度问题诊断...")
    
    # 1. 检查PyTorch状态
    check_torch_grad_state()
    
    # 2. 测试简单梯度
    test_simple_gradient()
    
    # 3. 测试数据处理
    test_data_processing()
    
    # 4. 测试模型梯度
    model_ok = test_model_gradients()
    
    # 5. 生成诊断报告
    logger.info("📋 诊断报告:")
    logger.info(f"  - PyTorch梯度状态: {'✅ 正常' if torch.is_grad_enabled() else '❌ 异常'}")
    logger.info(f"  - 模型梯度状态: {'✅ 正常' if model_ok else '❌ 异常'}")
    
    # 6. 提供修复建议
    if not torch.is_grad_enabled():
        logger.warning("⚠️ 修复建议: 在训练代码开始处添加 torch.set_grad_enabled(True)")
    
    if not model_ok:
        logger.warning("⚠️ 修复建议: 检查LoRA配置和模型参数设置")
    
    logger.info("🎯 诊断完成")

def fix_common_issues():
    """修复常见的梯度问题"""
    logger.info("🔧 修复常见梯度问题...")
    
    # 1. 确保梯度计算启用
    torch.set_grad_enabled(True)
    logger.info("✅ 启用梯度计算")
    
    # 2. 清理可能的梯度状态
    if hasattr(torch, '_C') and hasattr(torch._C, '_set_grad_enabled'):
        torch._C._set_grad_enabled(True)
        logger.info("✅ 底层梯度状态已重置")
    
    # 3. 检查CUDA状态（安全检查）
    try:
        if torch.cuda.is_available():
            logger.info(f"✅ CUDA可用")
            try:
                device_count = torch.cuda.device_count()
                logger.info(f"✅ CUDA设备数量: {device_count}")
            except:
                logger.warning("⚠️ CUDA设备信息获取失败")
        else:
            logger.info("ℹ️ CUDA不可用，将使用CPU")
    except Exception as e:
        logger.warning(f"⚠️ CUDA检查失败: {e}")
    
    logger.info("🎯 常见问题修复完成")

def main():
    """主函数"""
    logger.info("🚀 梯度调试工具启动...")
    
    # 修复常见问题
    fix_common_issues()
    
    # 诊断梯度问题
    diagnose_gradient_issue()
    
    logger.info("🎉 梯度调试完成")

if __name__ == "__main__":
    main()
