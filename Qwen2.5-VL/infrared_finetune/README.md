# 红外视频目标检测系统

基于Qwen2.5-VL的红外视频目标检测完整解决方案，包含数据处理、模型微调、目标检测和结果评估四个核心模块。

## 🎯 核心特性

### 1. 正确的视频合成方式
按照指定的帧间隔采样方式生成视频序列：

**第1组 (帧0-24):**
- 序列0: 帧0, 5, 10, 15, 20
- 序列1: 帧1, 6, 11, 16, 21  
- 序列2: 帧2, 7, 12, 17, 22
- 序列3: 帧3, 8, 13, 18, 23
- 序列4: 帧4, 9, 14, 19, 24

### 2. 解决张量形状问题
- **强制校验grid_thw维度有效性**: 确保frames、h、w均≥1
- **严格数学验证**: 保证num_tokens = frames × h × w
- **智能形状统一**: 统一特征形状时同步更新grid_thw
- **避免0维度错误**: 防止出现[0, 2, 496, 2]类型的无效形状

### 3. 数据集配置

| 序列 | 视频数 | 数据集 | 特点 |
|------|--------|--------|------|
| data01 | 60 | 测试集 | 256×256, 简单数字标注 |
| data02 | 30 | 训练集 | 256×256, 简单数字标注 |
| data04 | 15 | 训练集 | 256×256, 简单数字标注 |
| data05 | 15 | 训练集 | 256×256, 简单数字标注 |
| data06 | 15 | 训练集 | 256×256, 简单数字标注 |
| data07 | 15 | 训练集 | 256×256, 简单数字标注 |
| data23 | 30 | 测试集 | 640×512, 复杂标注文件名 |
| data25 | 60 | 训练集 | 640×512, 复杂标注文件名 |
| data26 | 60 | 训练集 | 640×512, 复杂标注文件名 |

## 📁 项目结构（已清理）

```
infrared_finetune/
├── data_processor_correct.py    # 数据处理模块
├── train_correct.py            # 模型微调模块
├── detect_correct.py           # 目标检测模块
├── evaluate_correct.py         # 结果评估模块
├── run_pipeline.py             # 主控制脚本
├── README.md                   # 说明文档
├── data/                       # 数据目录
│   ├── videos/                 # 生成的视频文件
│   ├── infrared_video_train.json  # 训练数据
│   └── infrared_video_test.json   # 测试数据
├── output/                     # 模型输出
└── results/                    # 检测结果
```

## 🚀 快速开始

### 运行完整流水线

```bash
# 运行所有步骤
python run_pipeline.py --steps all

# 分步执行
python run_pipeline.py --steps data          # 数据处理
python run_pipeline.py --steps train         # 模型微调
python run_pipeline.py --steps detect        # 目标检测
python run_pipeline.py --steps evaluate      # 结果评估

# 跳过已存在的结果
python run_pipeline.py --steps all --skip-existing
```

### 单独运行各模块

```bash
python data_processor_correct.py    # 数据处理
python train_correct.py            # 模型微调
python detect_correct.py           # 目标检测
python evaluate_correct.py         # 结果评估
```

## 🔧 核心技术解决方案

### 1. 张量形状问题解决

**问题**: `RuntimeError: shape '[0, 2, 496, 2]' is invalid for input of size 993`

**解决方案**:
```python
# 强制校验grid_thw维度有效性
frames = max(1, frames)  # 确保frames ≥ 1
h = max(1, h)           # 确保h ≥ 1  
w = max(1, w)           # 确保w ≥ 1

# 严格验证数学关系
calculated_tokens = frames * h * w
assert calculated_tokens == num_tokens
```

### 2. 视频处理优化

**正确的processor调用**:
```python
inputs = self.processor(
    videos=frames,  # 使用videos参数，不是images
    text=text,
    return_tensors="pt"
)

# 获取正确的视频特征
pixel_values = inputs.get('pixel_values_videos', 
                         inputs.get('pixel_values'))
```

### 3. 批量数据统一

**智能形状统一**:
```python
# 统一特征形状时同步更新grid_thw
for pv, grid in zip(pixel_values_videos, video_grid_thws):
    # 统一特征形状
    pv = adjust_to_target_shape(pv, target_shape)
    
    # 重新计算grid_thw
    new_grid = calculate_grid_thw(pv.shape[0], fixed_frames)
    unified_grid_thws.append(new_grid)
```

## 📊 评估指标

系统提供以下评估指标：

- **精确率 (Precision)**: TP / (TP + FP)
- **召回率 (Recall)**: TP / (TP + FN)  
- **F1分数**: 2 × Precision × Recall / (Precision + Recall)
- **IoU阈值**: 默认0.5，可调整

## 🎯 预期结果

完整流水线执行后，您将获得：

1. **处理后的视频数据**: 按正确的帧间隔采样生成
2. **微调后的模型**: 适应红外视频目标检测任务
3. **检测结果**: JSON格式的目标检测结果
4. **评估报告**: 详细的性能指标分析

## ⚠️ 注意事项

1. **内存管理**: 建议使用小批次大小(batch_size=1)避免GPU内存不足
2. **数据路径**: 确保所有数据路径正确配置
3. **模型权重**: 首次运行需要下载Qwen2.5-VL模型权重
4. **CUDA环境**: 确保CUDA环境正确配置

## 🔍 故障排除

### 常见问题

1. **张量形状错误**: 已通过grid_thw计算优化解决
2. **内存不足**: 减小batch_size或使用gradient_checkpointing
3. **数据加载失败**: 检查视频文件路径和格式
4. **模型加载错误**: 确认模型路径和权限

### 调试模式

```bash
# 启用详细日志
export PYTHONPATH=.
python -u run_pipeline.py --steps all 2>&1 | tee pipeline.log
```

## 📈 性能优化

1. **使用flash_attention_2**: 提升训练速度
2. **梯度检查点**: 减少内存使用
3. **混合精度训练**: 使用bf16加速训练
4. **数据并行**: 多GPU训练支持

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

本项目遵循MIT许可证。
