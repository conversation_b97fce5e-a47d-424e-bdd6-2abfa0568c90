{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 0.9523809523809523, "eval_steps": 500, "global_step": 50, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.09523809523809523, "grad_norm": 0.0, "learning_rate": 2.5e-06, "loss": 12.2138, "step": 5}, {"epoch": 0.19047619047619047, "grad_norm": 0.0, "learning_rate": 5.625e-06, "loss": 11.2061, "step": 10}, {"epoch": 0.2857142857142857, "grad_norm": 0.0, "learning_rate": 8.750000000000001e-06, "loss": 11.6875, "step": 15}, {"epoch": 0.38095238095238093, "grad_norm": 0.0, "learning_rate": 9.98867437523228e-06, "loss": 11.3604, "step": 20}, {"epoch": 0.47619047619047616, "grad_norm": 0.0, "learning_rate": 9.91964794299315e-06, "loss": 12.6917, "step": 25}, {"epoch": 0.5714285714285714, "grad_norm": 0.0, "learning_rate": 9.788754083424654e-06, "loss": 11.8717, "step": 30}, {"epoch": 0.6666666666666666, "grad_norm": 0.0, "learning_rate": 9.597638862757255e-06, "loss": 12.2426, "step": 35}, {"epoch": 0.7619047619047619, "grad_norm": 0.0, "learning_rate": 9.348705665778479e-06, "loss": 10.2761, "step": 40}, {"epoch": 0.8571428571428571, "grad_norm": 0.0, "learning_rate": 9.045084971874738e-06, "loss": 12.3837, "step": 45}, {"epoch": 0.9523809523809523, "grad_norm": 0.0, "learning_rate": 8.690594987436705e-06, "loss": 10.6473, "step": 50}], "logging_steps": 5, "max_steps": 156, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 50, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 1.2463169285326848e+16, "train_batch_size": 1, "trial_name": null, "trial_params": null}