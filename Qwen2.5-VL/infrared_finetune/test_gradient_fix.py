#!/usr/bin/env python3
"""
测试梯度修复的脚本
验证requires_grad设置是否正确
"""

import os
import torch
import logging
from pathlib import Path

# 设置环境变量避免CUDA问题
os.environ["CUDA_VISIBLE_DEVICES"] = ""

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_basic_requires_grad():
    """测试基础的requires_grad设置"""
    logger.info("🧪 测试基础requires_grad设置...")
    
    # 测试1: 创建需要梯度的张量
    x = torch.tensor([1.0, 2.0, 3.0], requires_grad=True)
    logger.info(f"x.requires_grad: {x.requires_grad}")
    
    # 测试2: 计算
    y = x.sum()
    logger.info(f"y.requires_grad: {y.requires_grad}")
    logger.info(f"y.grad_fn: {y.grad_fn}")
    
    # 测试3: 反向传播
    y.backward()
    logger.info(f"x.grad: {x.grad}")
    
    if x.grad is not None and y.requires_grad:
        logger.info("✅ 基础requires_grad测试通过")
        return True
    else:
        logger.error("❌ 基础requires_grad测试失败")
        return False

def test_tensor_operations_grad():
    """测试张量操作中的梯度传播"""
    logger.info("🔧 测试张量操作梯度传播...")
    
    # 创建需要梯度的张量
    a = torch.randn(2, 3, requires_grad=True)
    b = torch.randn(2, 3, requires_grad=True)
    
    logger.info(f"a.requires_grad: {a.requires_grad}")
    logger.info(f"b.requires_grad: {b.requires_grad}")
    
    # 各种操作
    c = a + b
    d = torch.stack([a, b])
    e = torch.cat([a, b], dim=0)
    
    logger.info(f"c.requires_grad: {c.requires_grad}")
    logger.info(f"d.requires_grad: {d.requires_grad}")
    logger.info(f"e.requires_grad: {e.requires_grad}")
    
    # 计算损失
    loss = c.sum() + d.sum() + e.sum()
    logger.info(f"loss.requires_grad: {loss.requires_grad}")
    logger.info(f"loss.grad_fn: {loss.grad_fn}")
    
    if loss.requires_grad and loss.grad_fn is not None:
        # 反向传播
        loss.backward()
        
        if a.grad is not None and b.grad is not None:
            logger.info("✅ 张量操作梯度传播测试通过")
            return True
        else:
            logger.error("❌ 梯度计算失败")
            return False
    else:
        logger.error("❌ 损失不需要梯度")
        return False

def test_model_parameters_grad():
    """测试模型参数的梯度设置"""
    logger.info("🤖 测试模型参数梯度设置...")
    
    # 创建简单模型
    class SimpleModel(torch.nn.Module):
        def __init__(self):
            super().__init__()
            self.linear1 = torch.nn.Linear(10, 5)
            self.linear2 = torch.nn.Linear(5, 1)
        
        def forward(self, x):
            x = self.linear1(x)
            x = torch.relu(x)
            x = self.linear2(x)
            return x
    
    model = SimpleModel()
    
    # 检查参数的requires_grad
    logger.info("模型参数梯度状态:")
    for name, param in model.named_parameters():
        logger.info(f"  {name}: requires_grad={param.requires_grad}")
    
    # 测试前向传播
    x = torch.randn(3, 10)  # 输入不需要梯度
    y = model(x)
    
    logger.info(f"输入x.requires_grad: {x.requires_grad}")
    logger.info(f"输出y.requires_grad: {y.requires_grad}")
    logger.info(f"输出y.grad_fn: {y.grad_fn}")
    
    # 计算损失
    target = torch.randn(3, 1)
    loss = torch.nn.functional.mse_loss(y, target)
    
    logger.info(f"损失loss.requires_grad: {loss.requires_grad}")
    logger.info(f"损失loss.grad_fn: {loss.grad_fn}")
    
    if loss.requires_grad and loss.grad_fn is not None:
        # 反向传播
        loss.backward()
        
        # 检查参数梯度
        has_grad = False
        for name, param in model.named_parameters():
            if param.grad is not None:
                has_grad = True
                logger.info(f"  {name}: 有梯度")
            else:
                logger.warning(f"  {name}: 无梯度")
        
        if has_grad:
            logger.info("✅ 模型参数梯度测试通过")
            return True
        else:
            logger.error("❌ 模型参数没有梯度")
            return False
    else:
        logger.error("❌ 损失不需要梯度")
        return False

def test_force_requires_grad():
    """测试强制设置requires_grad"""
    logger.info("🔧 测试强制设置requires_grad...")
    
    # 创建不需要梯度的张量
    x = torch.tensor([1.0, 2.0, 3.0], requires_grad=False)
    logger.info(f"初始x.requires_grad: {x.requires_grad}")
    
    # 强制设置需要梯度
    x.requires_grad = True
    logger.info(f"设置后x.requires_grad: {x.requires_grad}")
    
    # 测试计算
    y = x.sum()
    logger.info(f"y.requires_grad: {y.requires_grad}")
    logger.info(f"y.grad_fn: {y.grad_fn}")
    
    if y.requires_grad and y.grad_fn is not None:
        # 反向传播
        y.backward()
        
        if x.grad is not None:
            logger.info(f"x.grad: {x.grad}")
            logger.info("✅ 强制设置requires_grad测试通过")
            return True
        else:
            logger.error("❌ 强制设置后仍无梯度")
            return False
    else:
        logger.error("❌ 强制设置后仍不需要梯度")
        return False

def test_lora_like_parameters():
    """测试类似LoRA参数的梯度设置"""
    logger.info("🎯 测试LoRA类参数梯度设置...")
    
    # 创建模拟LoRA参数的模型
    class LoRALikeModel(torch.nn.Module):
        def __init__(self):
            super().__init__()
            # 基础参数（冻结）
            self.base_linear = torch.nn.Linear(10, 10)
            for param in self.base_linear.parameters():
                param.requires_grad = False
            
            # LoRA参数（可训练）
            self.lora_A = torch.nn.Parameter(torch.randn(10, 4))
            self.lora_B = torch.nn.Parameter(torch.randn(4, 10))
            
        def forward(self, x):
            # 基础变换
            base_out = self.base_linear(x)
            # LoRA变换
            lora_out = x @ self.lora_A @ self.lora_B
            return base_out + lora_out
    
    model = LoRALikeModel()
    
    # 检查参数状态
    logger.info("模型参数状态:")
    trainable_params = 0
    for name, param in model.named_parameters():
        logger.info(f"  {name}: requires_grad={param.requires_grad}")
        if param.requires_grad:
            trainable_params += 1
    
    logger.info(f"可训练参数数量: {trainable_params}")
    
    # 强制启用LoRA参数梯度
    for name, param in model.named_parameters():
        if 'lora' in name.lower():
            param.requires_grad = True
            logger.info(f"强制启用: {name}")
    
    # 重新检查
    trainable_params_after = sum(1 for p in model.parameters() if p.requires_grad)
    logger.info(f"强制启用后可训练参数数量: {trainable_params_after}")
    
    # 测试前向传播和反向传播
    x = torch.randn(5, 10)
    y = model(x)
    loss = y.sum()
    
    logger.info(f"损失requires_grad: {loss.requires_grad}")
    
    if loss.requires_grad:
        loss.backward()
        
        # 检查LoRA参数是否有梯度
        lora_has_grad = True
        for name, param in model.named_parameters():
            if 'lora' in name.lower():
                if param.grad is None:
                    logger.error(f"LoRA参数无梯度: {name}")
                    lora_has_grad = False
                else:
                    logger.info(f"LoRA参数有梯度: {name}")
        
        if lora_has_grad:
            logger.info("✅ LoRA类参数梯度测试通过")
            return True
        else:
            logger.error("❌ LoRA参数没有梯度")
            return False
    else:
        logger.error("❌ 损失不需要梯度")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始梯度修复测试...")
    
    # 确保梯度计算启用
    torch.set_grad_enabled(True)
    
    results = []
    
    # 运行各种测试
    results.append(test_basic_requires_grad())
    results.append(test_tensor_operations_grad())
    results.append(test_model_parameters_grad())
    results.append(test_force_requires_grad())
    results.append(test_lora_like_parameters())
    
    # 总结
    logger.info("📊 测试结果:")
    test_names = [
        "基础requires_grad",
        "张量操作梯度",
        "模型参数梯度",
        "强制设置梯度",
        "LoRA类参数梯度"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        logger.info(f"{name}: {'✅ 通过' if result else '❌ 失败'}")
    
    if all(results):
        logger.info("🎉 所有梯度测试通过！")
        logger.info("💡 requires_grad设置正确")
        logger.info("💡 可以安全运行训练代码")
    else:
        logger.error("❌ 部分梯度测试失败")
        logger.info("💡 请检查requires_grad设置")
    
    logger.info("🎯 测试完成")

if __name__ == "__main__":
    main()
