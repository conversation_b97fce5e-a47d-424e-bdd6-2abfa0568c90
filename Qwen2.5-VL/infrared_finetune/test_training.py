#!/usr/bin/env python3
"""
训练测试脚本
用于测试修复后的训练代码是否能正常工作
"""

import os
import json
import torch
import logging
from pathlib import Path
from transformers import AutoProcessor, Qwen2_5_VLForConditionalGeneration, TrainingArguments, Trainer
from peft import LoraConfig, get_peft_model, TaskType
from train_fixed_frames import InfraredVideoDataset, data_collator

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_dummy_data():
    """创建虚拟训练数据"""
    logger.info("📊 创建虚拟训练数据...")
    
    # 创建虚拟的训练数据
    dummy_data = [
        {
            "conversations": [
                {
                    "from": "human",
                    "value": "<video>\n这是一个红外视频序列，请检测其中的目标。"
                },
                {
                    "from": "gpt", 
                    "value": '{\n  "1": [],\n  "2": [],\n  "3": [],\n  "4": [],\n  "5": []\n}'
                }
            ],
            "video": "dummy_video.mp4"
        }
    ]
    
    # 保存虚拟数据
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    
    with open(data_dir / "test_train.json", 'w', encoding='utf-8') as f:
        json.dump(dummy_data, f, ensure_ascii=False, indent=2)
    
    logger.info("✅ 虚拟数据创建完成")
    return str(data_dir / "test_train.json")

def test_model_loading():
    """测试模型加载"""
    logger.info("🤖 测试模型加载...")
    
    model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
    
    try:
        # 加载processor
        processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
        logger.info("✅ Processor加载成功")
        
        # 加载模型
        model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.bfloat16,
            device_map="cpu",  # 使用CPU避免CUDA问题
            trust_remote_code=True
        )
        logger.info("✅ 基础模型加载成功")
        
        # 确保梯度计算启用
        torch.set_grad_enabled(True)
        model.train()
        
        # 应用LoRA
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            target_modules=["q_proj", "k_proj", "v_proj", "o_proj"],  # 减少目标模块
            inference_mode=False,
            r=8,  # 减小rank
            lora_alpha=8,
            lora_dropout=0.1,
            bias="none",
        )
        
        model = get_peft_model(model, lora_config)
        logger.info("✅ LoRA配置成功")
        
        # 检查可训练参数
        trainable_params = 0
        total_params = 0
        
        for name, param in model.named_parameters():
            total_params += param.numel()
            if param.requires_grad:
                trainable_params += param.numel()
        
        logger.info(f"总参数: {total_params:,}")
        logger.info(f"可训练参数: {trainable_params:,}")
        logger.info(f"可训练比例: {100 * trainable_params / total_params:.2f}%")
        
        if trainable_params > 0:
            logger.info("✅ 模型有可训练参数")
            return model, processor
        else:
            logger.error("❌ 模型没有可训练参数")
            return None, None
            
    except Exception as e:
        logger.error(f"❌ 模型加载失败: {e}")
        return None, None

def test_dataset():
    """测试数据集"""
    logger.info("📊 测试数据集...")
    
    # 创建虚拟数据
    data_file = create_dummy_data()
    
    try:
        # 加载数据
        with open(data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 创建processor（简化版）
        model_path = "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct"
        processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
        
        # 创建数据集
        dataset = InfraredVideoDataset(data, processor)
        logger.info(f"✅ 数据集创建成功，样本数: {len(dataset)}")
        
        # 测试数据加载
        try:
            sample = dataset[0]
            logger.info("✅ 数据样本加载成功")
            logger.info(f"样本键: {list(sample.keys())}")
            
            # 测试collator
            batch = [sample]
            collated = data_collator(batch)
            
            if collated is not None:
                logger.info("✅ 数据整理成功")
                logger.info(f"批次键: {list(collated.keys())}")
                return dataset
            else:
                logger.error("❌ 数据整理失败")
                return None
                
        except Exception as e:
            logger.error(f"❌ 数据处理失败: {e}")
            return None
            
    except Exception as e:
        logger.error(f"❌ 数据集测试失败: {e}")
        return None

def test_training_step():
    """测试训练步骤"""
    logger.info("🎯 测试训练步骤...")
    
    # 加载模型
    model, processor = test_model_loading()
    if model is None:
        logger.error("❌ 模型加载失败，跳过训练测试")
        return False
    
    # 测试数据集
    dataset = test_dataset()
    if dataset is None:
        logger.error("❌ 数据集创建失败，跳过训练测试")
        return False
    
    try:
        # 确保梯度计算启用
        torch.set_grad_enabled(True)
        model.train()
        
        # 创建训练参数
        training_args = TrainingArguments(
            output_dir="./test_output",
            per_device_train_batch_size=1,
            gradient_accumulation_steps=1,
            num_train_epochs=0.01,  # 很小的epoch数
            learning_rate=1e-5,
            logging_steps=1,
            save_steps=1000,  # 不保存
            remove_unused_columns=False,
            dataloader_num_workers=0,
            report_to="none",
        )
        
        # 创建trainer
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=dataset,
            data_collator=data_collator,
        )
        
        logger.info("✅ Trainer创建成功")
        
        # 测试一个训练步骤
        logger.info("🔄 测试训练步骤...")
        
        # 获取一个批次
        dataloader = trainer.get_train_dataloader()
        batch = next(iter(dataloader))
        
        logger.info(f"批次键: {list(batch.keys())}")
        
        # 手动执行一个训练步骤
        model.train()
        torch.set_grad_enabled(True)
        
        # 前向传播
        outputs = model(**batch)
        loss = outputs.loss
        
        logger.info(f"损失值: {loss.item()}")
        logger.info(f"损失requires_grad: {loss.requires_grad}")
        logger.info(f"损失grad_fn: {loss.grad_fn}")
        
        if loss.requires_grad and loss.grad_fn is not None:
            logger.info("✅ 前向传播成功，损失可以计算梯度")
            
            # 反向传播
            loss.backward()
            logger.info("✅ 反向传播成功")
            
            # 检查梯度
            grad_count = 0
            for name, param in model.named_parameters():
                if param.requires_grad and param.grad is not None:
                    grad_count += 1
            
            logger.info(f"有梯度的参数数量: {grad_count}")
            
            if grad_count > 0:
                logger.info("✅ 梯度计算成功")
                return True
            else:
                logger.error("❌ 没有参数有梯度")
                return False
        else:
            logger.error("❌ 损失不能计算梯度")
            return False
            
    except Exception as e:
        logger.error(f"❌ 训练步骤测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    logger.info("🚀 训练测试开始...")
    
    # 确保梯度计算启用
    torch.set_grad_enabled(True)
    
    # 运行测试
    success = test_training_step()
    
    if success:
        logger.info("🎉 训练测试成功！")
        logger.info("💡 修复建议已生效，可以开始正式训练")
    else:
        logger.error("❌ 训练测试失败")
        logger.info("💡 请检查以下几点:")
        logger.info("   1. 模型路径是否正确")
        logger.info("   2. 依赖包是否正确安装")
        logger.info("   3. 是否有足够的内存")
    
    logger.info("🎯 测试完成")

if __name__ == "__main__":
    main()
