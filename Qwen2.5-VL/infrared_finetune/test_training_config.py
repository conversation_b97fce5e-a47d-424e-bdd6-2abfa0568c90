#!/usr/bin/env python3
"""
测试训练配置是否合理
"""

import json
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_training_config():
    """测试训练配置"""
    logger.info("🧪 测试训练配置...")
    
    # 检查数据文件
    data_file = Path("data/infrared_video_train.json")
    if not data_file.exists():
        logger.error(f"❌ 训练数据文件不存在: {data_file}")
        return False
    
    # 加载数据
    with open(data_file, 'r', encoding='utf-8') as f:
        train_data = json.load(f)
    
    dataset_size = len(train_data)
    logger.info(f"数据集大小: {dataset_size}")
    
    # 训练配置
    per_device_batch_size = 1
    gradient_accumulation_steps = 4
    num_train_epochs = 3
    dataloader_drop_last = False
    
    # 计算训练步数
    effective_batch_size = per_device_batch_size * gradient_accumulation_steps
    steps_per_epoch = dataset_size // effective_batch_size
    if not dataloader_drop_last:
        steps_per_epoch += 1 if dataset_size % effective_batch_size > 0 else 0
    total_steps = steps_per_epoch * num_train_epochs
    
    logger.info(f"训练配置:")
    logger.info(f"  批次大小: {per_device_batch_size}")
    logger.info(f"  梯度累积: {gradient_accumulation_steps}")
    logger.info(f"  有效批次大小: {effective_batch_size}")
    logger.info(f"  训练轮数: {num_train_epochs}")
    logger.info(f"  丢弃最后批次: {dataloader_drop_last}")
    
    logger.info(f"训练步数计算:")
    logger.info(f"  每轮步数: {steps_per_epoch}")
    logger.info(f"  总训练步数: {total_steps}")
    
    # 估算训练时间
    estimated_time_per_step = 25  # 秒
    total_time_seconds = total_steps * estimated_time_per_step
    total_time_minutes = total_time_seconds / 60
    total_time_hours = total_time_minutes / 60
    
    logger.info(f"预计训练时间:")
    logger.info(f"  总秒数: {total_time_seconds}")
    logger.info(f"  总分钟数: {total_time_minutes:.1f}")
    logger.info(f"  总小时数: {total_time_hours:.2f}")
    
    # 检查配置是否合理
    if total_steps < 50:
        logger.warning("⚠️ 训练步数过少，可能训练不充分")
        logger.warning("建议: 增加训练轮数或减少梯度累积步数")
    elif total_steps > 1000:
        logger.warning("⚠️ 训练步数过多，可能训练时间过长")
        logger.warning("建议: 减少训练轮数或增加梯度累积步数")
    else:
        logger.info("✅ 训练配置合理")
    
    if total_time_hours > 5:
        logger.warning("⚠️ 预计训练时间超过5小时")
    elif total_time_hours < 0.5:
        logger.warning("⚠️ 预计训练时间少于30分钟，可能不够充分")
    else:
        logger.info("✅ 预计训练时间合理")
    
    return True

def check_data_samples():
    """检查数据样本"""
    logger.info("📊 检查数据样本...")
    
    data_file = Path("data/infrared_video_train.json")
    if not data_file.exists():
        logger.error("❌ 训练数据文件不存在")
        return False
    
    with open(data_file, 'r', encoding='utf-8') as f:
        train_data = json.load(f)
    
    logger.info(f"训练样本数量: {len(train_data)}")
    
    if len(train_data) > 0:
        sample = train_data[0]
        logger.info("样本格式检查:")
        logger.info(f"  键: {list(sample.keys())}")
        
        if 'conversations' in sample:
            logger.info(f"  对话数量: {len(sample['conversations'])}")
        
        if 'video' in sample:
            logger.info(f"  视频文件: {sample['video']}")
        
        logger.info("✅ 数据格式正确")
    else:
        logger.error("❌ 没有训练数据")
        return False
    
    return True

def main():
    """主函数"""
    logger.info("🚀 开始训练配置测试...")
    
    results = []
    
    # 检查数据样本
    results.append(check_data_samples())
    
    # 测试训练配置
    results.append(test_training_config())
    
    # 总结
    logger.info("📊 测试结果:")
    logger.info(f"数据检查: {'✅ 通过' if results[0] else '❌ 失败'}")
    logger.info(f"配置检查: {'✅ 通过' if results[1] else '❌ 失败'}")
    
    if all(results):
        logger.info("🎉 所有检查通过！")
        logger.info("💡 训练配置已优化，现在应该有合理的训练时间")
    else:
        logger.error("❌ 部分检查失败")
    
    logger.info("🎯 测试完成")

if __name__ == "__main__":
    main()
