#!/usr/bin/env python3
"""
红外微调项目测试脚本
验证整个流程的正确性和连贯性
"""

import os
import sys
import json
import logging
from pathlib import Path
import subprocess

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PipelineValidator:
    """流程验证器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.errors = []
        self.warnings = []
    
    def check_file_exists(self, file_path: str, description: str) -> bool:
        """检查文件是否存在"""
        full_path = self.project_root / file_path
        if full_path.exists():
            logger.info(f"✅ {description}: {full_path}")
            return True
        else:
            self.errors.append(f"❌ {description}: {full_path} 不存在")
            return False
    
    def check_directory_exists(self, dir_path: str, description: str) -> bool:
        """检查目录是否存在"""
        full_path = self.project_root / dir_path
        if full_path.exists() and full_path.is_dir():
            logger.info(f"✅ {description}: {full_path}")
            return True
        else:
            self.warnings.append(f"⚠️ {description}: {full_path} 不存在")
            return False
    
    def validate_python_syntax(self, file_path: str) -> bool:
        """验证Python文件语法"""
        try:
            with open(self.project_root / file_path, 'r', encoding='utf-8') as f:
                compile(f.read(), file_path, 'exec')
            logger.info(f"✅ 语法检查通过: {file_path}")
            return True
        except SyntaxError as e:
            self.errors.append(f"❌ 语法错误 {file_path}: {e}")
            return False
        except Exception as e:
            self.warnings.append(f"⚠️ 检查失败 {file_path}: {e}")
            return False
    
    def validate_imports(self, file_path: str) -> bool:
        """验证导入是否正确"""
        try:
            # 临时添加项目路径到sys.path
            sys.path.insert(0, str(self.project_root))
            
            # 尝试导入模块
            module_name = Path(file_path).stem
            __import__(module_name)
            
            logger.info(f"✅ 导入检查通过: {file_path}")
            return True
        except ImportError as e:
            self.warnings.append(f"⚠️ 导入警告 {file_path}: {e}")
            return False
        except Exception as e:
            self.warnings.append(f"⚠️ 导入检查失败 {file_path}: {e}")
            return False
        finally:
            # 移除临时路径
            if str(self.project_root) in sys.path:
                sys.path.remove(str(self.project_root))
    
    def validate_json_format(self, file_path: str) -> bool:
        """验证JSON文件格式"""
        try:
            full_path = self.project_root / file_path
            if not full_path.exists():
                return False
                
            with open(full_path, 'r', encoding='utf-8') as f:
                json.load(f)
            logger.info(f"✅ JSON格式正确: {file_path}")
            return True
        except json.JSONDecodeError as e:
            self.errors.append(f"❌ JSON格式错误 {file_path}: {e}")
            return False
        except Exception as e:
            self.warnings.append(f"⚠️ JSON检查失败 {file_path}: {e}")
            return False
    
    def check_data_consistency(self) -> bool:
        """检查数据一致性"""
        logger.info("检查数据一致性...")
        
        # 检查训练数据格式
        train_file = self.project_root / "data/infrared_video_train.json"
        if train_file.exists():
            try:
                with open(train_file, 'r', encoding='utf-8') as f:
                    train_data = json.load(f)
                
                if isinstance(train_data, list) and len(train_data) > 0:
                    # 检查第一个样本的格式
                    sample = train_data[0]
                    required_keys = ['conversations', 'video']
                    
                    for key in required_keys:
                        if key not in sample:
                            self.errors.append(f"❌ 训练数据缺少字段: {key}")
                            return False
                    
                    # 检查对话格式
                    conversations = sample['conversations']
                    if len(conversations) >= 2:
                        if conversations[0]['from'] == 'human' and conversations[1]['from'] == 'gpt':
                            logger.info("✅ 训练数据格式正确")
                            return True
                        else:
                            self.errors.append("❌ 对话格式不正确")
                            return False
                    else:
                        self.errors.append("❌ 对话数量不足")
                        return False
                else:
                    self.errors.append("❌ 训练数据为空")
                    return False
                    
            except Exception as e:
                self.errors.append(f"❌ 训练数据检查失败: {e}")
                return False
        else:
            self.warnings.append("⚠️ 训练数据文件不存在")
            return False
    
    def run_validation(self):
        """运行完整验证"""
        logger.info("🔍 开始流程验证...")
        logger.info("=" * 60)
        
        # 1. 检查核心文件
        logger.info("1. 检查核心文件...")
        core_files = [
            ("main.py", "主控制脚本"),
            ("data_processor.py", "数据预处理模块"),
            ("train_fixed_frames.py", "微调训练模块"),
            ("inference.py", "目标预测模块"),
            ("evaluator.py", "评估模块"),
            ("README.md", "项目说明")
        ]
        
        for file_path, desc in core_files:
            self.check_file_exists(file_path, desc)
        
        # 2. 检查目录结构
        logger.info("\n2. 检查目录结构...")
        directories = [
            ("data", "数据目录"),
            ("data/videos", "视频目录"),
            ("output", "输出目录")
        ]
        
        for dir_path, desc in directories:
            self.check_directory_exists(dir_path, desc)
        
        # 3. 语法检查
        logger.info("\n3. Python语法检查...")
        python_files = [
            "main.py",
            "data_processor.py", 
            "train_fixed_frames.py",
            "inference.py",
            "evaluator.py"
        ]
        
        for file_path in python_files:
            if (self.project_root / file_path).exists():
                self.validate_python_syntax(file_path)
        
        # 4. 导入检查
        logger.info("\n4. 导入检查...")
        for file_path in python_files:
            if (self.project_root / file_path).exists():
                self.validate_imports(file_path)
        
        # 5. 数据格式检查
        logger.info("\n5. 数据格式检查...")
        json_files = [
            "data/infrared_video_train.json",
            "data/infrared_video_test.json"
        ]
        
        for file_path in json_files:
            if (self.project_root / file_path).exists():
                self.validate_json_format(file_path)
        
        # 6. 数据一致性检查
        logger.info("\n6. 数据一致性检查...")
        self.check_data_consistency()
        
        # 7. 生成报告
        self.generate_report()
    
    def generate_report(self):
        """生成验证报告"""
        logger.info("\n" + "=" * 60)
        logger.info("验证报告")
        logger.info("=" * 60)
        
        if not self.errors and not self.warnings:
            logger.info("🎉 所有检查通过！项目结构完整，可以开始使用。")
        else:
            if self.errors:
                logger.error(f"发现 {len(self.errors)} 个错误:")
                for error in self.errors:
                    logger.error(f"  {error}")
            
            if self.warnings:
                logger.warning(f"发现 {len(self.warnings)} 个警告:")
                for warning in self.warnings:
                    logger.warning(f"  {warning}")
        
        # 使用建议
        logger.info("\n📋 使用建议:")
        logger.info("1. 确保激活Qwen conda环境: conda activate Qwen")
        logger.info("2. 运行完整流程: python main.py --step all")
        logger.info("3. 或分步运行:")
        logger.info("   - 数据预处理: python main.py --step preprocess")
        logger.info("   - 微调训练: python main.py --step train")
        logger.info("   - 目标预测: python main.py --step inference")
        logger.info("   - 模型评估: python main.py --step evaluate")
        
        # 保存报告
        report = {
            "validation_time": str(Path(__file__).stat().st_mtime),
            "errors": self.errors,
            "warnings": self.warnings,
            "status": "PASS" if not self.errors else "FAIL"
        }
        
        report_file = self.project_root / "validation_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"\n📄 详细报告已保存到: {report_file}")

def main():
    """主函数"""
    validator = PipelineValidator()
    validator.run_validation()

if __name__ == "__main__":
    main()
