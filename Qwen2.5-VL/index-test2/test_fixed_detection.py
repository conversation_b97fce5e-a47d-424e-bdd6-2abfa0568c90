#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的红外弱小目标检测系统
"""

import os
import sys
import subprocess
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    
    # 设置GPU设备
    os.environ['CUDA_VISIBLE_DEVICES'] = '2'
    
    # 检测参数
    model_path = "../Qwen2.5-VL-7B-Instruct"
    data_path = "../tiaozhanbei_datasets/images"
    annotation_path = "../tiaozhanbei_datasets/labels"
    class_json = "../tiaozhanbei_datasets/class.json"
    output_path = "Output/all_test_fixed_detection_results.json"
    
    # 测试参数
    sample_ratio = 0.2
    frame_group_size = 5
    iou_threshold = 0.3
    sequences = ["data03", "data05","data20","data21","data22","data24","data25"]  
    
    logger.info("=== 测试修复后的红外弱小目标检测系统 ===")
    logger.info(f"测试序列: {sequences}")
    logger.info(f"采样比例: {sample_ratio*100:.2f}%")
    logger.info(f"使用原始简单提示词，避免多帧检测失败")
    
    # 构建命令
    cmd = [
        sys.executable, "infrared_target_detection_2.py",
        "--model_path", model_path,
        "--data_path", data_path,
        "--annotation_path", annotation_path,
        "--class_json", class_json,
        "--output_path", output_path,
        "--device", "auto",
        "--sample_ratio", str(sample_ratio),
        "--frame_group_size", str(frame_group_size),
        "--iou_threshold", str(iou_threshold),
        "--sequences"
    ] + sequences
    
    logger.info(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 运行检测
        result = subprocess.run(cmd, check=True, capture_output=False, text=True)
        logger.info("测试完成！")
        
        # 检查输出文件
        if os.path.exists(output_path):
            logger.info(f"检测结果已保存到: {output_path}")
            
            # 简单统计
            import json
            with open(output_path, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            logger.info(f"检测到 {len(results)} 个目标")
            
            if results:
                # 统计各序列的检测数量
                seq_counts = {}
                for result in results:
                    seq_id = result['sequence_id']
                    seq_counts[seq_id] = seq_counts.get(seq_id, 0) + 1
                
                for seq_id, count in seq_counts.items():
                    logger.info(f"序列 {seq_id}: {count} 个目标")
                
                # 统计平均置信度
                avg_confidence = sum(r['confidence'] for r in results) / len(results)
                high_conf_count = sum(1 for r in results if r['confidence'] >= 0.5)
                precision_estimate = high_conf_count / len(results)
                
                logger.info(f"平均置信度: {avg_confidence:.3f}")
                logger.info(f"高置信度检测: {high_conf_count}/{len(results)} ({precision_estimate:.1%})")
                
                if precision_estimate >= 0.5:
                    logger.info("✅ 精确率满足要求 (≥ 0.5)")
                else:
                    logger.warning("⚠️ 精确率不足")
        
    except subprocess.CalledProcessError as e:
        logger.error(f"测试失败: {e}")
        return 1
    except KeyboardInterrupt:
        logger.info("用户中断测试")
        return 1
    except Exception as e:
        logger.error(f"未知错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
